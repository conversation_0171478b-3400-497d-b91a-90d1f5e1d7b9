This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.8)  10 OCT 2024 09:11
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**NCAR_Proposal.tex
(./NCAR_Proposal.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(c:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(c:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
)
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
)
(c:/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks19
\inpenc@posthook=\toks20
)
(c:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+phv on input line 11
6.

(c:/texlive/2024/texmf-dist/tex/latex/psnfss/t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
))
(c:/texlive/2024/texmf-dist/tex/latex/anyfontsize/anyfontsize.sty
Package: anyfontsize 2007/11/22 anyfontsize.sty by pts
)
(c:/texlive/2024/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(c:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(c:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(c:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count271
))
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(c:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen150
\Gin@req@width=\dimen151
)
(c:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(c:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(c:/texlive/2024/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2023/10/27 v2.16 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen152
\ttl@minus=\dimen153
\ttl@toksa=\toks21
\titlewidth=\dimen154
\titlewidthlast=\dimen155
\titlewidthfirst=\dimen156
)
(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count272
\l__pdf_internal_box=\box53
)
(./NCAR_Proposal.aux)
\openout1 = `NCAR_Proposal.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 41.
LaTeX Font Info:    ... okay on input line 41.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(42.67912pt, 512.14963pt, 42.67912pt)
* v-part:(T,H,B)=(71.13188pt, 731.23584pt, 42.67912pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=512.14963pt
* \textheight=731.23584pt
* \oddsidemargin=-29.59087pt
* \evensidemargin=-29.59087pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count273
\scratchdimen=\dimen157
\scratchbox=\box54
\nofMPsegments=\count274
\nofMParguments=\count275
\everyMPshowfont=\toks22
\MPscratchCnt=\count276
\MPscratchDim=\dimen158
\MPnumerator=\count277
\makeMPintoPDFobject=\count278
\everyMPtoPDFconversion=\toks23
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
LaTeX Font Info:    Trying to load font information for TS1+phv on input line 5
7.

(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ts1phv.fd
File: ts1phv.fd 2020/03/25 scalable font definitions for TS1/phv.
)
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 79.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 79.


[1

{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/2024/
texmf-dist/fonts/enc/dvips/base/8r.enc}]
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 95.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 95.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 95.


[2]

[3] (./NCAR_Proposal.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 3750 strings out of 473579
 56257 string characters out of 5732264
 1937960 words of memory out of 5000000
 26616 multiletter control sequences out of 15000+600000
 572940 words of font info for 54 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,5n,65p,1202b,182s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><c:/tex
live/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><c:/texlive/2024
/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><c:/texlive/2024/texmf-di
st/fonts/type1/public/amsfonts/cm/cmmi7.pfb><c:/texlive/2024/texmf-dist/fonts/t
ype1/public/amsfonts/cm/cmr10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/publi
c/amsfonts/cm/cmr7.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/
cm/cmr8.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.p
fb><c:/texlive/2024/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb><c:/texlive/
2024/texmf-dist/fonts/type1/urw/helvetic/uhvr8a.pfb>
Output written on NCAR_Proposal.pdf (3 pages, 98541 bytes).
PDF statistics:
 65 PDF objects out of 1000 (max. 8388607)
 39 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

