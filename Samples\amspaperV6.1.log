This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.8)  12 SEP 2024 10:49
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**amspaperV6.1.tex
(./amspaperV6.1.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
(./ametsocV6.1.cls
\c@section=\count194
\c@subsection=\count195
\c@subsubsection=\count196
\c@paragraph=\count197
\c@subparagraph=\count198
\c@figure=\count199
\c@table=\count266
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
 (c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(c:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen144
))
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count267
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count268
\leftroot@=\count269
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count270
\DOTSCASE@=\count271
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count272
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count273
\dotsspace@=\muskip17
\c@parentequation=\count274
\dspbrk@lvl=\count275
\tag@help=\toks19
\row@=\count276
\column@=\count277
\maxfields@=\count278
\andhelp@=\toks20
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
)
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup9
\symitalic=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
)
(c:/texlive/2024/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex su
pport for TeXGyreTermesX

`newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes a
nd its extensions (msharpe)
(c:/texlive/2024/texmf-dist/tex/latex/xpatch/xpatch.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-07-20 L3 programming layer (loader) 

(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count279
\l__pdf_internal_box=\box54
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands

(c:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-05-08 L3 Experimental document command parser
)
(c:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count280
))
(c:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(c:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(c:/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(c:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(c:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks23
\XKV@tempa@toks=\toks24
)
\XKV@depth=\count281
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(c:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(c:/texlive/2024/texmf-dist/tex/generic/xstring/xstring.sty
(c:/texlive/2024/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count282
\xs_countb=\count283
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
)
(c:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.

(c:/texlive/2024/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count284
\ntx@cnt=\count285

(c:/texlive/2024/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen153
\tx@y=\dimen154
\tx@x=\dimen155
\tx@tmpdima=\dimen156
\tx@tmpdimb=\dimen157
\tx@tmpdimc=\dimen158
\tx@tmpdimd=\dimen159
\tx@tmpdime=\dimen160
\tx@tmpdimf=\dimen161
\tx@dimA=\dimen162
\tx@dimAA=\dimen163
\tx@dimB=\dimen164
\tx@dimBB=\dimen165
\tx@dimC=\dimen166
LaTeX Info: Redefining \oldstylenums on input line 902.
)
(c:/texlive/2024/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/07/05 v1.752

`newtxmath' v1.752, 2024/07/05 Math macros based originally on txfonts (msharpe
) (c:/texlive/2024/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count286

(c:/texlive/2024/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count287
\tx@IsAlNum=\count288
\tx@tA=\toks25
\tx@tB=\toks26
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/ztmcm/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/ztmcm/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/ptm/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/ptm/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/ptm/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/ptm/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/ztmcm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ztmcm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup11
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 584.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/ztmcm/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/ztmcm/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup12
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup13
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
64.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/ztmcm/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/ztmcm/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup14
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks27
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf.sty
Package: epstopdf 2020-01-24 v2.11 Conversion with epstopdf on the fly (HO)

(c:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)

(c:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
))
(c:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(c:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(c:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
)))
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
)
(c:/texlive/2024/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2024/07/23 v4.3.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip54
\f@nch@O@elh=\skip55
\f@nch@O@erh=\skip56
\f@nch@O@olh=\skip57
\f@nch@O@orh=\skip58
\f@nch@O@elf=\skip59
\f@nch@O@erf=\skip60
\f@nch@O@olf=\skip61
\f@nch@O@orf=\skip62
)
(c:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip63
\bibsep=\skip64
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count289
)
(c:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/05/23 v1.9h multicolumn formatting (FMi)
\c@tracingmulticols=\count290
\mult@box=\box55
\multicol@leftmargin=\dimen167
\c@unbalance=\count291
\c@collectmore=\count292
\doublecol@number=\count293
\multicoltolerance=\count294
\multicolpretolerance=\count295
\full@width=\dimen168
\page@free=\dimen169
\premulticols=\dimen170
\postmulticols=\dimen171
\multicolsep=\skip65
\multicolbaselineskip=\skip66
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen172
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count296
\c@columnbadness=\count297
\c@finalcolumnbadness=\count298
\last@try=\dimen173
\multicolovershoot=\dimen174
\multicolundershoot=\dimen175
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count299
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/rotating.sty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
\c@r@tfl@t=\count300
\rotFPtop=\skip67
\rotFPbot=\skip68
\rot@float@box=\box101
\rot@mess@toks=\toks28
)
(c:/texlive/2024/texmf-dist/tex/latex/appendix/appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities


Package appendix Warning: No \appendix command in this document class!
(appendix)                Trying to create an appendix will probably fail.

\c@@pps=\count301
\c@@ppsavesec=\count302
\c@@ppsaveapp=\count303
) (c:/texlive/2024/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(c:/texlive/2024/texmf-dist/tex/latex/lineno/lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3
\linenopenalty=\count304
\output=\toks29
\linenoprevgraf=\count305
\linenumbersep=\dimen176
\linenumberwidth=\dimen177
\c@linenumber=\count306
\c@pagewiselinenumber=\count307
\c@LN@truepage=\count308
\c@internallinenumber=\count309
\c@internallinenumbers=\count310
\quotelinenumbersep=\dimen178
\bframerule=\dimen179
\bframesep=\dimen180
\bframebox=\box102
\linenoamsmath@ams@eqpen=\count311
LaTeX Info: Redefining \\ on input line 3180.
)
\c@loopnum=\count312
\thanksnum=\count313
\savethanksnum=\count314
)
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input lin
e 68.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 68.
 (./amspaperV6.1.aux)
\openout1 = `amspaperV6.1.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 68.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 68.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 68.
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 68.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
68.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 68.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 68.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 68.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 68.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 68.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 68.

(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count315
\scratchdimen=\dimen181
\scratchbox=\box103
\nofMPsegments=\count316
\nofMParguments=\count317
\everyMPshowfont=\toks30
\MPscratchCnt=\count318
\MPscratchDim=\dimen182
\MPnumerator=\count319
\makeMPintoPDFobject=\count320
\everyMPtoPDFconversion=\toks31
)
LaTeX Info: Command `\dddot' is already robust on input line 68.
LaTeX Info: Command `\ddddot' is already robust on input line 68.
\c@mv@tabular=\count321
\c@mv@boldtabular=\count322
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.4pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.4pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 5.0pt on input line 72.
LaTeX Font Info:    Trying to load font information for OT1+phv on input line 7
2.
 (c:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
LaTeX Font Info:    Font shape `OT1/phv/m/n' will be
(Font)              scaled to size 4.85pt on input line 72.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input lin
e 72.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 7
2.

(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 72.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8.8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 72.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6.6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 72.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
72.

(c:/texlive/2024/texmf-dist/tex/latex/gelasiomath/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
72.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
72.

(c:/texlive/2024/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.6pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.8pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 72.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.8pt on input line 72.


[1{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/202
4/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}{c:/texlive/2024/texmf-dist/
fonts/enc/dvips/base/8r.enc}]

[2]
LaTeX Font Info:    Trying to load font information for OT1+ntxtt on input line
 85.
 (c:/texlive/2024/texmf-dist/tex/latex/newtx/ot1ntxtt.fd
File: ot1ntxtt.fd 2012/04/20 v3.1
)
LaTeX Font Info:    Font shape `OT1/ntxtt/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.

Underfull \hbox (badness 1515) in paragraph at lines 85--94
 \OT1/minntx/m/n/12 SIG-NIF-I-CANCE STATE-MENT: En-ter sig-nif-i-cance state-me
nt here, no more than 120
 []

LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 100.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/sc' will be
(Font)              scaled to size 12.0pt on input line 131.


[3{c:/texlive/2024/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf-pc.enc}]

Package natbib Warning: Citation `Becker+Schmitz2003' on page 4 undefined on in
put line 149.


Package natbib Warning: Citation `Becker+Schmitz2003' on page 4 undefined on in
put line 150.

LaTeX Font Info:    Freeze math alphabet allocation in version normal.
(Font)              Allocated math groups: 15 (local: 2) on input line 178.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 12.0pt on input line 178.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 8.8pt on input line 178.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 6.6pt on input line 178.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 178.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 178.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 179.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 179.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 179.
LaTeX Font Info:    Undo math alphabet allocation in version normal on input li
ne 179.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 180.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 180.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 12.0pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 8.8pt on input line 182.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 6.6pt on input line 182.
LaTeX Font Info:    Freeze math alphabet allocation in version bold.
(Font)              Allocated math groups: 15 (local: 2) on input line 182.
LaTeX Font Info:    Trying to load font information for OT1+qhv on input line 1
82.
(c:/texlive/2024/texmf-dist/tex/latex/tex-gyre/ot1qhv.fd
File: ot1qhv.fd 2009/09/25 v1.2 font definition file for OT1/qhv
)
LaTeX Font Info:    Font shape `OT1/qhv/b/n' will be
(Font)              scaled to size 11.28003pt on input line 182.
LaTeX Font Info:    Font shape `OT1/qhv/b/n' will be
(Font)              scaled to size 8.27202pt on input line 182.
LaTeX Font Info:    Font shape `OT1/qhv/b/n' will be
(Font)              scaled to size 6.20401pt on input line 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    Undo math alphabet allocation in version bold on input line
 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 182.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 182.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 184.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 184.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 185.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 185.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 186.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 186.


[4{c:/texlive/2024/texmf-dist/fonts/enc/dvips/tex-gyre/q-rm.enc}]
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 190.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 190.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 190.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 190.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 191.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 191.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 191.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 191.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 191.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 191.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 192.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 192.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 192.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 192.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 192.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 192.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 192.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 192.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 198.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 198.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 198.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 198.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 198.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 198.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 199.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 199.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 201.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 201.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 205.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 205.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 205.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 205.


LaTeX Warning: File `figure01.pdf' not found on input line 221.


! Package pdftex.def Error: File `figure01.pdf' not found: using draft setting.


See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.221 ...ncludegraphics[width=19pc]{figure01.pdf}}
                                                  
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.5pt on input line 223.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/sc' will be
(Font)              scaled to size 10.5pt on input line 223.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.5pt on input line 223.

Package natbib Warning: Citation `Knutti2008' on page 5 undefined on input line
 223.


LaTeX Warning: `h' float specifier changed to `ht'.


Underfull \vbox (badness 1515) has occurred while \output is active []



[5]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.0pt on input line 231.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.


LaTeX Font Warning: Font shape `OT1/cmr/bx/n' in size <5.5> not available
(Font)              size <5> substituted on input line 234.


LaTeX Font Warning: Font shape `OML/cmm/b/it' in size <5.5> not available
(Font)              size <5> substituted on input line 234.


LaTeX Font Warning: Font shape `OMS/cmsy/b/n' in size <5.5> not available
(Font)              size <5> substituted on input line 234.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 234.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6.2> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 234.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5.5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 234.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.0pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.2pt on input line 234.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 234.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 236.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 236.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 236.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 236.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 236.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 236.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 236.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 236.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 243.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 243.


[6]
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 345.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 345.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 347.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 347.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 348.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 348.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 348.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 348.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 349.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 349.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 353.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 353.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 354.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 354.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 354.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 354.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 355.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 355.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 356.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 356.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 357.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 357.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 358.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 358.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 364.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 364.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 364.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 364.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 367.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 367.

Underfull \vbox (badness 1178) has occurred while \output is active []



[7

]

LaTeX Warning: File `figure01.pdf' not found on input line 372.


! Package pdftex.def Error: File `figure01.pdf' not found: using draft setting.


See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.372 \includegraphics[width=19pc]{figure01.pdf}
                                                
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `FigOne.pdf' not found on input line 378.


! Package pdftex.def Error: File `FigOne.pdf' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.378 \includegraphics[width=27pc]{FigOne.pdf}
                                              
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 386.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 386.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 396.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 396.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 8.8pt on input line 403.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 403.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 403.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 405.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 405.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 406.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 406.


[8]
LaTeX Font Info:    Trying to load font information for TS1+ntxtlf on input lin
e 416.
 (c:/texlive/2024/texmf-dist/tex/latex/newtx/ts1ntxtlf.fd
File: ts1ntxtlf.fd 2015/01/18 v1.0 fd file for TS1/ntxtlf
)
LaTeX Font Info:    Font shape `TS1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 416.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 438.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 438.


[9{c:/texlive/2024/texmf-dist/fonts/enc/dvips/tex-gyre/q-ts1.enc}]
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 483.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 483.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 485.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 485.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 485.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 485.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 491.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 491.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 495.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 495.


[10]
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 504.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 504.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 508.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 508.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 514.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 514.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 516.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 516.
LaTeX Font Info:    No math alphabet change to frozen version normal on input l
ine 517.
LaTeX Font Info:    No math alphabet change to frozen version bold on input lin
e 517.

No file amspaperV6.1.bbl.

Package natbib Warning: There were undefined citations.



[11] (./amspaperV6.1.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2020/03/25>
 ***********


LaTeX Font Warning: Size substitutions with differences
(Font)              up to 0.5pt have occurred.

 ) 
Here is how much of TeX's memory you used:
 9220 strings out of 473579
 134099 string characters out of 5732264
 1936960 words of memory out of 5000000
 31867 multiletter control sequences out of 15000+600000
 641669 words of font info for 198 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 95i,14n,107p,311b,360s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2024/texmf-dist/fonts/type1/public/newtx/NewTXMI.pfb><c:/texlive/
2024/texmf-dist/fonts/type1/public/newtx/NewTXMI7.pfb><c:/texlive/2024/texmf-di
st/fonts/type1/public/tex-gyre/qhvb.pfb><c:/texlive/2024/texmf-dist/fonts/type1
/public/tex-gyre/qtmr.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/newtx/
txmiaX.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/newtx/txsys.pfb><c:/t
exlive/2024/texmf-dist/fonts/type1/public/txfonts/txtt.pfb><c:/texlive/2024/tex
mf-dist/fonts/type1/urw/helvetic/uhvr8a.pfb><c:/texlive/2024/texmf-dist/fonts/t
ype1/public/newtx/ztmb.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/newtx
/ztmr.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/newtx/ztmri.pfb>
Output written on amspaperV6.1.pdf (11 pages, 205856 bytes).
PDF statistics:
 103 PDF objects out of 1000 (max. 8388607)
 66 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

