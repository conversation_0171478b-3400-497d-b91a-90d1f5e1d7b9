This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  31 JUL 2025 08:52
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**./Reviewer_Response_Reviewer_2.tex
(Reviewer_Response_Reviewer_2.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen142
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count268
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count269
\leftroot@=\count270
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count271
\DOTSCASE@=\count272
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count273
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count274
\dotsspace@=\muskip17
\c@parentequation=\count275
\dspbrk@lvl=\count276
\tag@help=\toks18
\row@=\count277
\column@=\count278
\maxfields@=\count279
\andhelp@=\toks19
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count280
\l__siunitx_number_exponent_fixed_int=\count281
\l__siunitx_number_min_decimal_int=\count282
\l__siunitx_number_min_integer_int=\count283
\l__siunitx_number_round_precision_int=\count284
\l__siunitx_number_lower_threshold_int=\count285
\l__siunitx_number_upper_threshold_int=\count286
\l__siunitx_number_group_first_int=\count287
\l__siunitx_number_group_size_int=\count288
\l__siunitx_number_group_minimum_int=\count289
\l__siunitx_angle_tmp_dim=\dimen151
\l__siunitx_angle_marker_box=\box54
\l__siunitx_angle_unit_box=\box55
\l__siunitx_compound_count_int=\count290

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count291
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
\l__siunitx_table_tmp_box=\box56
\l__siunitx_table_tmp_dim=\dimen152
\l__siunitx_table_column_width_dim=\dimen153
\l__siunitx_table_integer_box=\box57
\l__siunitx_table_decimal_box=\box58
\l__siunitx_table_uncert_box=\box59
\l__siunitx_table_before_box=\box60
\l__siunitx_table_after_box=\box61
\l__siunitx_table_before_dim=\dimen154
\l__siunitx_table_carry_dim=\dimen155
\l__siunitx_unit_tmp_int=\count292
\l__siunitx_unit_position_int=\count293
\l__siunitx_unit_total_int=\count294

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen156
\ar@mcellbox=\box62
\extrarowheight=\dimen157
\NC@list=\toks22
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box63
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen158
\Gin@req@width=\dimen159
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count295
\float@exts=\toks24
\float@box=\box64
\@float@everytoks=\toks25
\@floatcapt=\box65
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.st
y
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen160
\captionmargin=\dimen161
\caption@leftmargin=\dimen162
\caption@rightmargin=\dimen163
\caption@width=\dimen164
\caption@indent=\dimen165
\caption@parindent=\dimen166
\caption@hangindent=\dimen167
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count296
\c@continuedfloat=\count297
Package caption Info: float package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip56
\bibsep=\skip57
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count298
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\dvipsnam.d
ef
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.s
ty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfesca
pe.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.st
y
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.s
ty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\ge
ttitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringe
nc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen168
\Hy@linkcounter=\count300
\Hy@pagecounter=\count301

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.s
ty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count302

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count303
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen169

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigint
calc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count304
\Field@Width=\dimen170
\Fld@charsize=\dimen171
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.s
ty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count305
\c@Item=\count306
\c@Hfootnote=\count307
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.de
f
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.
sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count308
\c@bookmark@seq@number=\count309

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\reru
nfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uni
quecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count310
\Gm@cntv=\count311
\c@Gm@tempcnt=\count312
\Gm@bindingoffset=\dimen172
\Gm@wd@mp=\dimen173
\Gm@odd@mp=\dimen174
\Gm@even@mp=\dimen175
\Gm@layoutwidth=\dimen176
\Gm@layoutheight=\dimen177
\Gm@layouthoffset=\dimen178
\Gm@layoutvoffset=\dimen179
\Gm@dimlist=\toks26

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.s
ty
Package: enumitem 2025/01/19 v3.10 Customized lists
\labelindent=\skip59
\enit@outerparindent=\dimen180
\enit@toks=\toks27
\enit@inbox=\box66
\enit@count@id=\count313
\enitdp@description=\count314
)
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 20.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count315
\l__pdf_internal_box=\box67
) (Reviewer_Response_Reviewer_2.aux)
\openout1 = `Reviewer_Response_Reviewer_2.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 20.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count316
\scratchdimen=\dimen181
\scratchbox=\box68
\nofMPsegments=\count317
\nofMParguments=\count318
\everyMPshowfont=\toks28
\MPscratchCnt=\count319
\MPscratchDim=\dimen182
\MPnumerator=\count320
\makeMPintoPDFobject=\count321
\everyMPtoPDFconversion=\toks29
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring OFF on input line 20.
 (Reviewer_Response_Reviewer_2.out) (Reviewer_Response_Reviewer_2.out)
\@outlinefile=\write3
\openout3 = `Reviewer_Response_Reviewer_2.out'.


*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]

[2]

[3]

[4]

[5]

[6]

[7]

[8]

[9]

[10]

[11] (Reviewer_Response_Reviewer_2.bbl)

[12] (Reviewer_Response_Reviewer_2.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
Package rerunfilecheck Info: File `Reviewer_Response_Reviewer_2.out' has not ch
anged.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 16083 strings out of 473904
 314411 string characters out of 5724713
 1942908 words of memory out of 5000000
 38710 multiletter control sequences out of 15000+600000
 564134 words of font info for 58 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,6n,80p,923b,1055s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts
/cm/cmbx10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/pu
blic/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/f
onts/type1/public/amsfonts/cm/cmitt10.pfb><C:/Users/<USER>/AppData/Local/Pro
grams/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi10.pfb><C:/Users/<USER>/AppD
ata/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi7.pfb><C:/Users/<USER>
yeTunde/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr10.pfb>
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/c
m/cmr12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publi
c/amsfonts/cm/cmr17.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts
/type1/public/amsfonts/cm/cmr7.pfb><C:/Users/<USER>/AppData/Local/Programs/M
iKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Users/<USER>/AppData/Loc
al/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmti10.pfb><C:/Users/<USER>
e/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmtt10.pfb>
Output written on Reviewer_Response_Reviewer_2.pdf (12 pages, 217133 bytes).
PDF statistics:
 231 PDF objects out of 1000 (max. 8388607)
 97 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

