This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: NCAR_proposal_main2.aux
The style file: apalike.bst
Database file #1: NCAR_Proposal.bib
You've used 8 entries,
            1935 wiz_defined-function locations,
            559 strings with 6401 characters,
and the built_in function-call counts, 3776 in all, are:
= -- 346
> -- 229
< -- 1
+ -- 82
- -- 82
* -- 372
:= -- 680
add.period$ -- 24
call.type$ -- 8
change.case$ -- 81
chr.to.int$ -- 8
cite$ -- 8
duplicate$ -- 109
empty$ -- 222
format.name$ -- 92
if$ -- 702
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 8
newline$ -- 43
num.names$ -- 24
pop$ -- 64
preamble$ -- 1
purify$ -- 81
quote$ -- 0
skip$ -- 85
stack$ -- 0
substring$ -- 237
swap$ -- 8
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 48
warning$ -- 0
while$ -- 24
width$ -- 0
write$ -- 106
