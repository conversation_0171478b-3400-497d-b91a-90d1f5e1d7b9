This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
The style file: IEEEtran.bst
I found no \citation commands---while reading file main.aux
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: references.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 0 entries,
            4087 wiz_defined-function locations,
            821 strings with 7287 characters,
and the built_in function-call counts, 104 in all, are:
= -- 0
> -- 0
< -- 0
+ -- 0
- -- 0
* -- 19
:= -- 27
add.period$ -- 0
call.type$ -- 0
change.case$ -- 0
chr.to.int$ -- 0
cite$ -- 0
duplicate$ -- 0
empty$ -- 1
format.name$ -- 0
if$ -- 3
int.to.chr$ -- 0
int.to.str$ -- 0
missing$ -- 0
newline$ -- 23
num.names$ -- 0
pop$ -- 0
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 1
stack$ -- 0
substring$ -- 0
swap$ -- 0
text.length$ -- 0
text.prefix$ -- 0
top$ -- 5
type$ -- 0
warning$ -- 0
while$ -- 0
width$ -- 0
write$ -- 22
(There was 1 error message)
