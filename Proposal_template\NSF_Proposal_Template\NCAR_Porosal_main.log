This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.8)  10 OCT 2024 09:29
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**NCAR_Porosal_main.tex
(./NCAR_Porosal_main.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
(./NSF.cls
Document Class: NSF 
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(c:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(c:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
)
(c:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX

(c:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(c:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(c:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(c:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(c:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(c:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(c:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(c:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count271
)
(c:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count272
)
(c:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen150
\Hy@linkcounter=\count273
\Hy@pagecounter=\count274

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(c:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count275

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count276

(c:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen151

(c:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(c:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count277
\Field@Width=\dimen152
\Fld@charsize=\dimen153
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(c:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count278
\c@Item=\count279
\c@Hfootnote=\count280
)
Package hyperref Info: Driver: hpdftex.

(c:/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX

(c:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count281
\c@bookmark@seq@number=\count282

(c:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(c:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip51
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(c:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen154
\Gin@req@width=\dimen155
)
(c:/texlive/2024/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2024/07/23 v4.3.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip52
\f@nch@O@elh=\skip53
\f@nch@O@erh=\skip54
\f@nch@O@olh=\skip55
\f@nch@O@orh=\skip56
\f@nch@O@elf=\skip57
\f@nch@O@erf=\skip58
\f@nch@O@olf=\skip59
\f@nch@O@orf=\skip60
)
(c:/texlive/2024/texmf-dist/tex/latex/titlecaps/titlecaps.sty
Package: titlecaps 2022/04/12\ 1.3\ Routines for setting rich-text input into T
itling Caps

(c:/texlive/2024/texmf-dist/tex/latex/ifnextok/ifnextok.sty
Package: ifnextok 2011/06/27 v0.3 test next token (UL)
)
(c:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\c@lcword@index=\count283
\c@word@count=\count284
\c@lc@words=\count285
\c@dia@count=\count286
\c@arg@@@index=\count287
)
(c:/texlive/2024/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2023/10/27 v2.16 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip61
\aftertitleunit=\skip62
\ttl@plus=\dimen156
\ttl@minus=\dimen157
\ttl@toksa=\toks19
\titlewidth=\dimen158
\titlewidthlast=\dimen159
\titlewidthfirst=\dimen160
))
(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count288
\l__pdf_internal_box=\box53
)

LaTeX Warning: Unused global option(s):
    [timesnewroman].

(./NCAR_Porosal_main.aux)
\openout1 = `NCAR_Porosal_main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(c:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(c:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 5.

(./NCAR_Porosal_main.out) (./NCAR_Porosal_main.out)
\@outlinefile=\write3
\openout3 = `NCAR_Porosal_main.out'.


(c:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count289
\scratchdimen=\dimen161
\scratchbox=\box54
\nofMPsegments=\count290
\nofMParguments=\count291
\everyMPshowfont=\toks20
\MPscratchCnt=\count292
\MPscratchDim=\dimen162
\MPnumerator=\count293
\makeMPintoPDFobject=\count294
\everyMPtoPDFconversion=\toks21
) (c:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(c:/texlive/2024/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.jpeg,.png,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(c:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Overfull \hbox (7.20517pt too wide) in paragraph at lines 21--22
[]\OT1/cmr/m/n/10.95 The TIMED/SABER (Ther-mo-sphere Iono-sphere Meso-sphere En
-er-get-ics and Dy-nam-ics/Sounding
 []



[1

{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}]
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 50.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 50.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 50.


[1{c:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}]
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 66.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 66.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 66.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 66.



[2]

[3] (./NCAR_Porosal_main.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
 ***********
Package rerunfilecheck Info: File `NCAR_Porosal_main.out' has not changed.
(rerunfilecheck)             Checksum: 97D1FB2FE0A535C6A5F420E740357B93;1753.
 ) 
Here is how much of TeX's memory you used:
 9856 strings out of 473579
 151826 string characters out of 5732264
 1936960 words of memory out of 5000000
 32629 multiletter control sequences out of 15000+600000
 563580 words of font info for 54 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 90i,11n,93p,606b,511s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb><c:/tex
live/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><c:/texlive/2024
/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><c:/texlive/2024/texmf-di
st/fonts/type1/public/amsfonts/cm/cmmi12.pfb><c:/texlive/2024/texmf-dist/fonts/
type1/public/amsfonts/cm/cmmi8.pfb><c:/texlive/2024/texmf-dist/fonts/type1/publ
ic/amsfonts/cm/cmr10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfont
s/cm/cmr8.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10
.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1095.pfb>
Output written on NCAR_Porosal_main.pdf (4 pages, 109599 bytes).
PDF statistics:
 117 PDF objects out of 1000 (max. 8388607)
 92 compressed objects within 1 object stream
 17 named destinations out of 1000 (max. 500000)
 81 words of extra memory for PDF output out of 10000 (max. 10000000)

