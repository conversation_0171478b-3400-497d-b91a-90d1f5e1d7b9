This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.8)  22 OCT 2024 15:19
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**NCAR_proposal_main2.tex
(./NCAR_proposal_main2.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(c:/texlive/2024/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(c:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)

(c:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
\caption@tempdima=\dimen142
\captionmargin=\dimen143
\caption@leftmargin=\dimen144
\caption@rightmargin=\dimen145
\caption@width=\dimen146
\caption@indent=\dimen147
\caption@parindent=\dimen148
\caption@hangindent=\dimen149
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count268
\c@continuedfloat=\count269
)
(c:/texlive/2024/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(c:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(c:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(c:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen150
\Gm@wd@mp=\dimen151
\Gm@odd@mp=\dimen152
\Gm@even@mp=\dimen153
\Gm@layoutwidth=\dimen154
\Gm@layoutheight=\dimen155
\Gm@layouthoffset=\dimen156
\Gm@layoutvoffset=\dimen157
\Gm@dimlist=\toks18
)
(c:/texlive/2024/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(c:/texlive/2024/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2023/10/27 v2.16 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen158
\ttl@minus=\dimen159
\ttl@toksa=\toks19
\titlewidth=\dimen160
\titlewidthlast=\dimen161
\titlewidthfirst=\dimen162
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip53

For additional information on amsmath, use the `?' option.
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen163
))
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen164
)
(c:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen165
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip17
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks21
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks22
\eqnshift@=\dimen166
\alignsep@=\dimen167
\tagshift@=\dimen168
\tagwidth@=\dimen169
\totwidth@=\dimen170
\lineht@=\dimen171
\@envbody=\toks23
\multlinegap=\skip54
\multlinetaggap=\skip55
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(c:/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks25
\thm@bodyfont=\toks26
\thm@headfont=\toks27
\thm@notefont=\toks28
\thm@headpunct=\toks29
\thm@preskip=\skip56
\thm@postskip=\skip57
\thm@headsep=\skip58
\dth@everypar=\toks30
)
(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(c:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(c:/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip59
\bibsep=\skip60
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count285
)
(c:/texlive/2024/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `amsthm' support loaded on input line 3026.
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 2
7.

(c:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box55
)
(./NCAR_proposal_main2.aux)
\openout1 = `NCAR_proposal_main2.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for U+msa on input line 46.

(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 46.


(c:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{c:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/2024/
texmf-dist/fonts/enc/dvips/base/8r.enc}]

[2] (./NCAR_proposal_main2.bbl)

[3] (./NCAR_proposal_main2.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-07-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 6182 strings out of 473579
 108974 string characters out of 5732264
 1937960 words of memory out of 5000000
 29055 multiletter control sequences out of 15000+600000
 571805 words of font info for 66 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,5n,65p,1594b,156s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><c:/tex
live/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb><c:/texlive/2024
/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><c:/texlive/2024/texmf-dis
t/fonts/type1/public/amsfonts/cm/cmr12.pfb><c:/texlive/2024/texmf-dist/fonts/ty
pe1/public/amsfonts/cm/cmr8.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/
amsfonts/cm/cmsy10.pfb><c:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/
cm/cmsy8.pfb><c:/texlive/2024/texmf-dist/fonts/type1/urw/times/utmb8a.pfb><c:/t
exlive/2024/texmf-dist/fonts/type1/urw/times/utmr8a.pfb><c:/texlive/2024/texmf-
dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on NCAR_proposal_main2.pdf (3 pages, 120743 bytes).
PDF statistics:
 65 PDF objects out of 1000 (max. 8388607)
 39 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

