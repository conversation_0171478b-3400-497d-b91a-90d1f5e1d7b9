This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  16 SEP 2025 14:51
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**n:/SABER_ALL_DATA/SABER_PROCESS_EP_DAILY_2024/MIL_JAS_Paper/MIL_JAS_Paper_Reviewer_Response_Dec_2025.tex
(n:/SABER_ALL_DATA/SABER_PROCESS_EP_DAILY_2024/MIL_JAS_Paper/MIL_JAS_Paper_Reviewer_Response_Dec_2025.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks20
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen159
\Gin@req@width=\dimen160
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count283
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count284
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen161
\Hy@linkcounter=\count285
\Hy@pagecounter=\count286
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count287
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count288
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen162
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count289
\Field@Width=\dimen163
\Fld@charsize=\dimen164
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count290
\c@Item=\count291
\c@Hfootnote=\count292
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count293
\c@bookmark@seq@number=\count294
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip54
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/cleveref\cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count295
\l__siunitx_number_exponent_fixed_int=\count296
\l__siunitx_number_min_decimal_int=\count297
\l__siunitx_number_min_integer_int=\count298
\l__siunitx_number_round_precision_int=\count299
\l__siunitx_number_lower_threshold_int=\count300
\l__siunitx_number_upper_threshold_int=\count301
\l__siunitx_number_group_first_int=\count302
\l__siunitx_number_group_size_int=\count303
\l__siunitx_number_group_minimum_int=\count304
\l__siunitx_angle_tmp_dim=\dimen165
\l__siunitx_angle_marker_box=\box54
\l__siunitx_angle_unit_box=\box55
\l__siunitx_compound_count_int=\count305
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\translations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages (CN)
)
\l__siunitx_table_tmp_box=\box56
\l__siunitx_table_tmp_dim=\dimen166
\l__siunitx_table_column_width_dim=\dimen167
\l__siunitx_table_integer_box=\box57
\l__siunitx_table_decimal_box=\box58
\l__siunitx_table_uncert_box=\box59
\l__siunitx_table_before_box=\box60
\l__siunitx_table_after_box=\box61
\l__siunitx_table_before_dim=\dimen168
\l__siunitx_table_carry_dim=\dimen169
\l__siunitx_unit_tmp_int=\count306
\l__siunitx_unit_position_int=\count307
\l__siunitx_unit_total_int=\count308
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen170
\ar@mcellbox=\box62
\extrarowheight=\dimen171
\NC@list=\toks24
\extratabsurround=\skip55
\backup@length=\skip56
\ar@cellbox=\box63
))
Package translations Info: No language package found. I am going to use `english' as default language. on input line 22.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count309
\l__pdf_internal_box=\box64
) (MIL_JAS_Paper_Reviewer_Response_Dec_2025.aux)
\openout1 = `MIL_JAS_Paper_Reviewer_Response_Dec_2025.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count310
\scratchdimen=\dimen172
\scratchbox=\box65
\nofMPsegments=\count311
\nofMParguments=\count312
\everyMPshowfont=\toks25
\MPscratchCnt=\count313
\MPscratchDim=\dimen173
\MPnumerator=\count314
\makeMPintoPDFobject=\count315
\everyMPtoPDFconversion=\toks26
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring OFF on input line 22.
 (MIL_JAS_Paper_Reviewer_Response_Dec_2025.out) (MIL_JAS_Paper_Reviewer_Response_Dec_2025.out)
\@outlinefile=\write3
\openout3 = `MIL_JAS_Paper_Reviewer_Response_Dec_2025.out'.

 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `translations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' for `english'. on input line 22.
LaTeX Font Info:    Trying to load font information for U+msa on input line 25.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 25.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]

[2]

[3]

[4]

[5]

[6]

[7]

[8] (MIL_JAS_Paper_Reviewer_Response_Dec_2025.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
Package rerunfilecheck Info: File `MIL_JAS_Paper_Reviewer_Response_Dec_2025.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 16084 strings out of 473904
 320358 string characters out of 5724713
 1935908 words of memory out of 5000000
 38767 multiletter control sequences out of 15000+600000
 567169 words of font info for 71 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,6n,79p,746b,984s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr17.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb>
Output written on MIL_JAS_Paper_Reviewer_Response_Dec_2025.pdf (8 pages, 84633 bytes).
PDF statistics:
 112 PDF objects out of 1000 (max. 8388607)
 43 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

