This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  16 SEP 2025 14:01
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**./MIL_JAS_Paper_Review_Sep_2025.tex
(MIL_JAS_Paper_Review_Sep_2025.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(ametsocv6.1.cls
\c@section=\count194
\c@subsection=\count195
\c@subsubsection=\count196
\c@paragraph=\count197
\c@subparagraph=\count198
\c@figure=\count199
\c@table=\count266
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen144
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count267
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count268
\leftroot@=\count269
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count270
\DOTSCASE@=\count271
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count272
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count273
\dotsspace@=\muskip17
\c@parentequation=\count274
\dspbrk@lvl=\count275
\tag@help=\toks19
\row@=\count276
\column@=\count277
\maxfields@=\count278
\andhelp@=\toks20
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup9
\symitalic=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex su
pport for TeXGyreTermesX

`newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes a
nd its extensions (msharpe)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xpatch\xpatch.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2024-05-27 L3 programming layer (loader) 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count279
\l__pdf_internal_box=\box54
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3packages/xparse\x
parse.sty
Package: xparse 2024-05-08 L3 Experimental document command parser
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count280
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkeyval.t
ex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xkeyval\xkvutils.
tex
\XKV@toks=\toks23
\XKV@tempa@toks=\toks24
)
\XKV@depth=\count281
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xstring\xstring.sty

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/xstring\xstring.t
ex
\xs_counta=\count282
\xs_countb=\count283
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/carlisle\scalefnt.s
ty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/binhex\binhex.tex
)
\ntx@tmpcnta=\count284
\ntx@cnt=\count285

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fontaxes\fontaxes.s
ty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen153
\tx@y=\dimen154
\tx@x=\dimen155
\tx@tmpdima=\dimen156
\tx@tmpdimb=\dimen157
\tx@tmpdimc=\dimen158
\tx@tmpdimd=\dimen159
\tx@tmpdime=\dimen160
\tx@tmpdimf=\dimen161
\tx@dimA=\dimen162
\tx@dimAA=\dimen163
\tx@dimB=\dimen164
\tx@dimBB=\dimen165
\tx@dimC=\dimen166
LaTeX Info: Redefining \oldstylenums on input line 902.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\newtxmath.sty
Package: newtxmath 2024/09/22 v1.754

`newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/oberdiek\centernot.
sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count286

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/binhex\binhex.tex
)
\tx@Isdigit=\count287
\tx@IsAlNum=\count288
\tx@tA=\toks25
\tx@tB=\toks26
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/ztmcm/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/ztmcm/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/ptm/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/ptm/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/ptm/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/ptm/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/ztmcm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ztmcm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup11
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 584.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/ztmcm/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/ztmcm/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup12
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup13
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
64.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/ztmcm/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/ztmcm/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup14
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks27
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df.sty
Package: epstopdf 2020-01-24 v2.11 Conversion with epstopdf on the fly (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/grfext\grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.s
ty
Package: fancyhdr 2025/01/07 v5.1.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip54
\f@nch@offset@elh=\skip55
\f@nch@offset@erh=\skip56
\f@nch@offset@olh=\skip57
\f@nch@offset@orh=\skip58
\f@nch@offset@elf=\skip59
\f@nch@offset@erf=\skip60
\f@nch@offset@olf=\skip61
\f@nch@offset@orf=\skip62
\f@nch@height=\skip63
\f@nch@footalignment=\skip64
\f@nch@widthL=\skip65
\f@nch@widthC=\skip66
\f@nch@widthR=\skip67
\@temptokenb=\toks28
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip68
\bibsep=\skip69
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count289
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\indentfirst.s
ty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\multicol.sty
Package: multicol 2024/05/23 v1.9h multicolumn formatting (FMi)
\c@tracingmulticols=\count290
\mult@box=\box55
\multicol@leftmargin=\dimen167
\c@unbalance=\count291
\c@collectmore=\count292
\doublecol@number=\count293
\multicoltolerance=\count294
\multicolpretolerance=\count295
\full@width=\dimen168
\page@free=\dimen169
\premulticols=\dimen170
\postmulticols=\dimen171
\multicolsep=\skip70
\multicolbaselineskip=\skip71
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen172
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count296
\c@columnbadness=\count297
\c@finalcolumnbadness=\count298
\last@try=\dimen173
\multicolovershoot=\dimen174
\multicolundershoot=\dimen175
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count299
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\rotating.s
ty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
\c@r@tfl@t=\count300
\rotFPtop=\skip72
\rotFPbot=\skip73
\rot@float@box=\box101
\rot@mess@toks=\toks29
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/appendix\appendix.s
ty
Package: appendix 2020/02/08 v1.2c extra appendix facilities


Package appendix Warning: No \appendix command in this document class!
(appendix)                Trying to create an appendix will probably fail.

\c@@pps=\count301
\c@@ppsavesec=\count302
\c@@ppsaveapp=\count303
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.s
ty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lineno\lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3
\linenopenalty=\count304
\output=\toks30
\linenoprevgraf=\count305
\linenumbersep=\dimen176
\linenumberwidth=\dimen177
\c@linenumber=\count306
\c@pagewiselinenumber=\count307
\c@LN@truepage=\count308
\c@internallinenumber=\count309
\c@internallinenumbers=\count310
\quotelinenumbersep=\dimen178
\bframerule=\dimen179
\bframesep=\dimen180
\bframebox=\box102
\linenoamsmath@ams@eqpen=\count311
LaTeX Info: Redefining \\ on input line 3180.
)
\c@loopnum=\count312
\thanksnum=\count313
\savethanksnum=\count314
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ragged2e\ragged2e.s
ty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip74
\RaggedLeftLeftskip=\skip75
\RaggedRightLeftskip=\skip76
\CenteringRightskip=\skip77
\RaggedLeftRightskip=\skip78
\RaggedRightRightskip=\skip79
\CenteringParfillskip=\skip80
\RaggedLeftParfillskip=\skip81
\RaggedRightParfillskip=\skip82
\JustifyingParfillskip=\skip83
\CenteringParindent=\skip84
\RaggedLeftParindent=\skip85
\RaggedRightParindent=\skip86
\JustifyingParindent=\skip87
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\adjustbox
.sty
Package: adjustbox 2022/10/17 v1.3a Adjusting TeX boxes (trim, clip, ...)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\adjcalc.s
ty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back
-ends (calc, etex, pgfmath)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\trimclip.
sty
Package: trimclip 2020/08/19 v1.2 Trim and clip general TeX material

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/collectbox\collectb
ox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box103
)
\tc@llx=\dimen181
\tc@lly=\dimen182
\tc@urx=\dimen183
\tc@ury=\dimen184
Package trimclip Info: Using driver 'tc-pdftex.def'.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/adjustbox\tc-pdftex
.def
File: tc-pdftex.def 2019/01/04 v2.2 Clipping driver for pdftex
))
\adjbox@Width=\dimen185
\adjbox@Height=\dimen186
\adjbox@Depth=\dimen187
\adjbox@Totalheight=\dimen188
\adjbox@pwidth=\dimen189
\adjbox@pheight=\dimen190
\adjbox@pdepth=\dimen191
\adjbox@ptotalheight=\dimen192

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ifoddpage\ifoddpage
.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count315
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/varwidth\varwidth.s
ty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box104
\sift@deathcycles=\count316
\@vwid@loff=\dimen193
\@vwid@roff=\dimen194
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.s
ty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip88
\multirow@cntb=\count317
\multirow@dima=\skip89
\bigstrutjot=\dimen195
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/placeins\placeins.s
ty
Package: placeins 2005/04/18  v 2.2
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/colortbl\colortbl.s
ty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2024/06/14 v2.6d Tabular extension package (FMi)
\col@sep=\dimen196
\ar@mcellbox=\box105
\extrarowheight=\dimen197
\NC@list=\toks31
\extratabsurround=\skip90
\backup@length=\skip91
\ar@cellbox=\box106
)
\everycr=\toks32
\minrowclearance=\skip92
\rownum=\count318
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.s
ty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen198
\lightrulewidth=\dimen199
\cmidrulewidth=\dimen256
\belowrulesep=\dimen257
\belowbottomsep=\dimen258
\aboverulesep=\dimen259
\abovetopsep=\dimen260
\cmidrulesep=\dimen261
\cmidrulekern=\dimen262
\defaultaddspace=\dimen263
\@cmidla=\count319
\@cmidlb=\count320
\@aboverulesep=\dimen264
\@belowrulesep=\dimen265
\@thisruleclass=\count321
\@lastruleclass=\count322
\@thisrulewidth=\dimen266
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tabularray\tabularr
ay.sty
Package: tabularray 2024-02-16 v2024A Typeset tabulars and arrays with LaTeX3
\l__tblr_a_int=\count323
\l__tblr_c_int=\count324
\l__tblr_r_int=\count325
\l__tblr_d_dim=\dimen267
\l__tblr_h_dim=\dimen268
\l__tblr_o_dim=\dimen269
\l__tblr_p_dim=\dimen270
\l__tblr_q_dim=\dimen271
\l__tblr_r_dim=\dimen272
\l__tblr_s_dim=\dimen273
\l__tblr_t_dim=\dimen274
\l__tblr_v_dim=\dimen275
\l__tblr_w_dim=\dimen276
\l__tblr_a_box=\box107
\l__tblr_b_box=\box108
\l__tblr_c_box=\box109
\l__tblr_d_box=\box110
\g__tblr_table_count_int=\count326
\c@colnum=\count327
\c@rowcount=\count328
\c@colcount=\count329
\abovesep=\dimen277
\belowsep=\dimen278
\leftsep=\dimen279
\rightsep=\dimen280
\g_tblr_level_int=\count330
\g__tblr_data_row_key_count_int=\count331
\g__tblr_data_column_key_count_int=\count332
\g__tblr_data_cell_key_count_int=\count333
\g__tblr_array_int=\count334
\l__tblr_key_count_int=\count335
\l__tblr_key_quotient_int=\count336
\l__tblr_key_quotient_two_int=\count337
\l__tblr_key_remainder_int=\count338
\g__tblr_data_str_value_count_int=\count339
\rulewidth=\dimen281
\l__tblr_strut_dp_dim=\dimen282
\l__tblr_strut_ht_dim=\dimen283
\g__tblr_cell_wd_dim=\dimen284
\g__tblr_cell_ht_dim=\dimen285
\g__tblr_cell_head_dim=\dimen286
\g__tblr_cell_foot_dim=\dimen287
\l__column_target_dim=\dimen288
\l__tblr_caption_box=\box111
\l__tblr_caption_left_box=\box112
\l__tblr_row_head_box=\box113
\l__tblr_row_foot_box=\box114
\l__tblr_row_head_foot_dim=\dimen289
\tablewidth=\dimen290
\l__tblr_table_firsthead_box=\box115
\l__tblr_table_middlehead_box=\box116
\l__tblr_table_lasthead_box=\box117
\l__tblr_table_firstfoot_box=\box118
\l__tblr_table_middlefoot_box=\box119
\l__tblr_table_lastfoot_box=\box120
\l__tblr_remain_height_dim=\dimen291
\l__tblr_long_from_int=\count340
\l__tblr_long_to_int=\count341
\l__tblr_curr_i_int=\count342
\l__tblr_prev_i_int=\count343
\l__tblr_table_page_int=\count344
\l__tblr_table_head_box=\box121
\l__tblr_table_foot_box=\box122
\l__tblr_table_head_foot_dim=\dimen292
\l__tblr_table_head_body_foot_dim=\dimen293
\l__tblr_table_box=\box123
\l__tblr_table_hlines_box=\box124
\l__tblr_hline_box=\box125
\l__tblr_row_box=\box126
\l__tblr_col_o_wd_dim=\dimen294
\l__tblr_col_b_wd_dim=\dimen295
\l__tblr_hline_leftskip_dim=\dimen296
\l__tblr_hline_rightskip_dim=\dimen297
\l__tblr_row_ht_dim=\dimen298
\l__tblr_row_dp_dim=\dimen299
\l__tblr_row_abovesep_dim=\dimen300
\l__tblr_row_belowsep_dim=\dimen301
\l__tblr_row_vlines_box=\box127
\l__tblr_vline_box=\box128
\l__tblr_cell_box=\box129
\l__row_upper_dim=\dimen302
\l__row_lower_dim=\dimen303
\l__row_vpace_dim=\dimen304
\l__tblr_vline_aboveskip_dim=\dimen305
\l__tblr_vline_belowskip_dim=\dimen306
\l__tblr_cell_wd_dim=\dimen307
\l__tblr_cell_ht_dim=\dimen308
\l__tblr_diag_box=\box130
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/diagbox\diagbox.sty
Package: diagbox 2020/02/09 v2.3 Making table heads with diagonal lines
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.sty
Package: pict2e 2020/09/30 v0.4b Improved picture commands (HjG,RN,JT)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\pict2e.cfg
File: pict2e.cfg 2016/02/05 v0.1u pict2e configuration for teTeX/TeXLive
)
Package pict2e Info: Driver file: pdftex.def on input line 112.
Package pict2e Info: Driver file for pict2e: p2e-pdftex.def on input line 114.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pict2e\p2e-pdftex.d
ef
File: p2e-pdftex.def 2016/02/05 v0.1u Driver-dependant file (RN,HjG,JT)
)
\pIIe@GRAPH=\toks33
\@arclen=\dimen309
\@arcrad=\dimen310
\pIIe@tempdima=\dimen311
\pIIe@tempdimb=\dimen312
\pIIe@tempdimc=\dimen313
\pIIe@tempdimd=\dimen314
\pIIe@tempdime=\dimen315
\pIIe@tempdimf=\dimen316
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count345
\calc@Bcount=\count346
\calc@Adimen=\dimen317
\calc@Bdimen=\dimen318
\calc@Askip=\skip93
\calc@Bskip=\skip94
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count347
\calc@Cskip=\skip95
)
\diagbox@boxa=\box131
\diagbox@boxb=\box132
\diagbox@boxm=\box133
\diagbox@wd=\dimen319
\diagbox@ht=\dimen320
\diagbox@insepl=\dimen321
\diagbox@insepr=\dimen322
\diagbox@outsepl=\dimen323
\diagbox@outsepr=\dimen324
)
\figwidth=\skip96

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/siunitx\siunitx.sty
Package: siunitx 2024-06-24 v3.3.19 A comprehensive (SI) units package
\l__siunitx_number_uncert_offset_int=\count348
\l__siunitx_number_exponent_fixed_int=\count349
\l__siunitx_number_min_decimal_int=\count350
\l__siunitx_number_min_integer_int=\count351
\l__siunitx_number_round_precision_int=\count352
\l__siunitx_number_lower_threshold_int=\count353
\l__siunitx_number_upper_threshold_int=\count354
\l__siunitx_number_group_first_int=\count355
\l__siunitx_number_group_size_int=\count356
\l__siunitx_number_group_minimum_int=\count357
\l__siunitx_angle_tmp_dim=\dimen325
\l__siunitx_angle_marker_box=\box134
\l__siunitx_angle_unit_box=\box135
\l__siunitx_compound_count_int=\count358

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations\transl
ations.sty
Package: translations 2022/02/05 v1.12 internationalization of LaTeX2e packages
 (CN)
)
\l__siunitx_table_tmp_box=\box136
\l__siunitx_table_tmp_dim=\dimen326
\l__siunitx_table_column_width_dim=\dimen327
\l__siunitx_table_integer_box=\box137
\l__siunitx_table_decimal_box=\box138
\l__siunitx_table_uncert_box=\box139
\l__siunitx_table_before_box=\box140
\l__siunitx_table_after_box=\box141
\l__siunitx_table_before_dim=\dimen328
\l__siunitx_table_carry_dim=\dimen329
\l__siunitx_unit_tmp_int=\count359
\l__siunitx_unit_position_int=\count360
\l__siunitx_unit_total_int=\count361
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/cleveref\cleveref.s
ty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: no abbreviation of names on input line 7852.
)
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input lin
e 83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 83.
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 83.
(MIL_JAS_Paper_Review_Sep_2025.aux)
\openout1 = `MIL_JAS_Paper_Review_Sep_2025.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 83.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 83.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 83.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 83.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 83.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count362
\scratchdimen=\dimen330
\scratchbox=\box142
\nofMPsegments=\count363
\nofMParguments=\count364
\everyMPshowfont=\toks34
\MPscratchCnt=\count365
\MPscratchDim=\dimen331
\MPnumerator=\count366
\makeMPintoPDFobject=\count367
\everyMPtoPDFconversion=\toks35
)
LaTeX Info: Command `\dddot' is already robust on input line 83.
LaTeX Info: Command `\ddddot' is already robust on input line 83.
\c@mv@tabular=\count368
\c@mv@boldtabular=\count369

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ninecolors\ninecolo
rs.sty
Package: ninecolors 2022-02-13 v2022D Select colors with proper color contrast
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/translations/dicts\
translations-basic-dictionary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 83.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.4pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.4pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 5.0pt on input line 85.
LaTeX Font Info:    Trying to load font information for OT1+phv on input line 8
5.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
LaTeX Font Info:    Font shape `OT1/phv/m/n' will be
(Font)              scaled to size 4.85pt on input line 85.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input lin
e 85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 8
5.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 85.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8.8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 85.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6.6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 85.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.6pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.8pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 85.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.8pt on input line 85.


[1{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/User
s/ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc
}{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/base/8r.enc}
]

[2]
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 112.

Underfull \vbox (badness 10000) has occurred while \output is active []



[3]
Underfull \vbox (badness 10000) has occurred while \output is active []



[4]
Underfull \vbox (badness 10000) has occurred while \output is active []



[5]
LaTeX Font Info:    Trying to load font information for OT1+ntxtt on input line
 125.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\ot1ntxtt.fd
File: ot1ntxtt.fd 2012/04/20 v3.1
)
LaTeX Font Info:    Font shape `OT1/ntxtt/m/n' will be
(Font)              scaled to size 12.0pt on input line 125.


[6]
Underfull \vbox (badness 2111) has occurred while \output is active []



[7]
<Figures/Temp_Profile_SABER_2003_001_05.jpeg, id=31, 505.89pt x 722.7pt>
File: Figures/Temp_Profile_SABER_2003_001_05.jpeg Graphic file (type jpg)
<use Figures/Temp_Profile_SABER_2003_001_05.jpeg>
Package pdftex.def Info: Figures/Temp_Profile_SABER_2003_001_05.jpeg  used on i
nput line 141.
(pdftex.def)             Requested size: 180.0pt x 239.23877pt.
<Figures/Temp_Profile_SABER_2003_001_00.jpeg, id=33, 505.89pt x 722.7pt>
File: Figures/Temp_Profile_SABER_2003_001_00.jpeg Graphic file (type jpg)
<use Figures/Temp_Profile_SABER_2003_001_00.jpeg>
Package pdftex.def Info: Figures/Temp_Profile_SABER_2003_001_00.jpeg  used on i
nput line 141.
(pdftex.def)             Requested size: 168.0pt x 235.72652pt.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/sc' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Calculating math sizes for size <10.5> on input line 143.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.


LaTeX Font Warning: Font shape `OT1/cmr/bx/n' in size <10.5> not available
(Font)              size <10.95> substituted on input line 143.


LaTeX Font Warning: Font shape `OML/cmm/b/it' in size <10.5> not available
(Font)              size <10.95> substituted on input line 143.


LaTeX Font Warning: Font shape `OMS/cmsy/b/n' in size <10.5> not available
(Font)              size <10.95> substituted on input line 143.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10.5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 143.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7.66495> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 143.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5.77502> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 143.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.5pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.66495pt on input line 143.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.77502pt on input line 143.

LaTeX Warning: `!h' float specifier changed to `!ht'.



[8]
<Figures/All_Profile_MIL_Counts_with_Trends.jpeg, id=38, 856.1586pt x 513.3579p
t>
File: Figures/All_Profile_MIL_Counts_with_Trends.jpeg Graphic file (type jpg)
<use Figures/All_Profile_MIL_Counts_with_Trends.jpeg>
Package pdftex.def Info: Figures/All_Profile_MIL_Counts_with_Trends.jpeg  used 
on input line 149.
(pdftex.def)             Requested size: 360.0pt x 215.85167pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[9{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/newtx/ntx-o
t1-tlf-pc.enc} <./Figures/All_Profile_MIL_Counts_with_Trends.jpeg> <./Figures/T
emp_Profile_SABER_2003_001_05.jpeg> <./Figures/Temp_Profile_SABER_2003_001_00.j
peg>]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 6.6pt on input line 162.


[10

]
<Figures/SABER_2010_2020_sf_kp_enso_qbo_plot.pdf, id=49, 722.7pt x 433.62pt>
File: Figures/SABER_2010_2020_sf_kp_enso_qbo_plot.pdf Graphic file (type pdf)
<use Figures/SABER_2010_2020_sf_kp_enso_qbo_plot.pdf>
Package pdftex.def Info: Figures/SABER_2010_2020_sf_kp_enso_qbo_plot.pdf  used 
on input line 170.
(pdftex.def)             Requested size: 360.0pt x 207.83444pt.


[11 <./Figures/SABER_2010_2020_sf_kp_enso_qbo_plot.pdf>]
Underfull \vbox (badness 2111) has occurred while \output is active []



[12]
<Figures/seasonal_grouped_bar_plot.jpg, id=132, 1069.1142pt x 394.47375pt>
File: Figures/seasonal_grouped_bar_plot.jpg Graphic file (type jpg)
<use Figures/seasonal_grouped_bar_plot.jpg>
Package pdftex.def Info: Figures/seasonal_grouped_bar_plot.jpg  used on input l
ine 184.
(pdftex.def)             Requested size: 360.0pt x 132.83119pt.


[13 <./Figures/seasonal_grouped_bar_plot.jpg>]
<Figures/Mean_MIL_top_height_Summer_map.jpeg, id=139, 615.7404pt x 341.3553pt>
File: Figures/Mean_MIL_top_height_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_height_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_height_Summer_map.jpeg  used on i
nput line 212.
(pdftex.def)             Requested size: 123.60004pt x 62.91132pt.
<Figures/Mean_MIL_top_height_Autumn_map.jpeg, id=141, 615.7404pt x 341.3553pt>
File: Figures/Mean_MIL_top_height_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_height_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_height_Autumn_map.jpeg  used on i
nput line 212.
(pdftex.def)             Requested size: 105.60004pt x 62.85101pt.
<Figures/Mean_MIL_top_height_Winter_map.jpeg, id=143, 615.7404pt x 341.3553pt>
File: Figures/Mean_MIL_top_height_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_height_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_height_Winter_map.jpeg  used on i
nput line 212.
(pdftex.def)             Requested size: 105.60004pt x 62.85101pt.
<Figures/Mean_MIL_top_height_Spring_map.jpeg, id=145, 615.7404pt x 341.3553pt>
File: Figures/Mean_MIL_top_height_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_height_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_height_Spring_map.jpeg  used on i
nput line 212.
(pdftex.def)             Requested size: 130.79993pt x 60.91pt.
<Figures/Mean_MIL_base_height_Summer_map.jpeg, id=147, 615.7404pt x 341.3553pt>

File: Figures/Mean_MIL_base_height_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_height_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_height_Summer_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 123.60004pt x 58.05388pt.
<Figures/Mean_MIL_base_height_Autumn_map.jpeg, id=149, 615.7404pt x 341.3553pt>

File: Figures/Mean_MIL_base_height_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_height_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_height_Autumn_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 105.60004pt x 57.99823pt.
<Figures/Mean_MIL_base_height_Winter_map.jpeg, id=151, 615.7404pt x 341.3553pt>

File: Figures/Mean_MIL_base_height_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_height_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_height_Winter_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 105.60004pt x 57.99823pt.
<Figures/Mean_MIL_base_height_Spring_map.jpeg, id=153, 615.7404pt x 341.3553pt>

File: Figures/Mean_MIL_base_height_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_height_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_height_Spring_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 130.79993pt x 56.20709pt.
<Figures/Mean_MIL_height_diff_Summer_map.jpeg, id=155, 631.03755pt x 341.3553pt
>
File: Figures/Mean_MIL_height_diff_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_height_diff_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_height_diff_Summer_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 123.60004pt x 74.85016pt.
<Figures/Mean_MIL_height_diff_Autumn_map.jpeg, id=157, 631.03755pt x 341.3553pt
>
File: Figures/Mean_MIL_height_diff_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_height_diff_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_height_diff_Autumn_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 105.60004pt x 74.04019pt.
<Figures/Mean_MIL_height_diff_Winter_map.jpeg, id=159, 631.03755pt x 341.3553pt
>
File: Figures/Mean_MIL_height_diff_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_height_diff_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_height_diff_Winter_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 105.60004pt x 74.04019pt.
<Figures/Mean_MIL_height_diff_Spring_map.jpeg, id=161, 631.03755pt x 341.3553pt
>
File: Figures/Mean_MIL_height_diff_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_height_diff_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_height_diff_Spring_map.jpeg  used on 
input line 212.
(pdftex.def)             Requested size: 130.79993pt x 74.84062pt.


[14 <./Figures/Mean_MIL_top_height_Summer_map.jpeg> <./Figures/Mean_MIL_top_hei
ght_Autumn_map.jpeg> <./Figures/Mean_MIL_top_height_Winter_map.jpeg> <./Figures
/Mean_MIL_top_height_Spring_map.jpeg> <./Figures/Mean_MIL_base_height_Summer_ma
p.jpeg> <./Figures/Mean_MIL_base_height_Autumn_map.jpeg> <./Figures/Mean_MIL_ba
se_height_Winter_map.jpeg> <./Figures/Mean_MIL_base_height_Spring_map.jpeg> <./
Figures/Mean_MIL_height_diff_Summer_map.jpeg> <./Figures/Mean_MIL_height_diff_A
utumn_map.jpeg> <./Figures/Mean_MIL_height_diff_Winter_map.jpeg> <./Figures/Mea
n_MIL_height_diff_Spring_map.jpeg>]
LaTeX Font Info:    Trying to load font information for TS1+ntxtlf on input lin
e 223.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/newtx\ts1ntxtlf.fd
File: ts1ntxtlf.fd 2015/01/18 v1.0 fd file for TS1/ntxtlf
)
LaTeX Font Info:    Font shape `TS1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 223.

Underfull \vbox (badness 10000) has occurred while \output is active []



[15]
<Figures/Mean_MIL_top_temp_Summer_map.jpeg, id=181, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_top_temp_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_temp_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_temp_Summer_map.jpeg  used on inp
ut line 242.
(pdftex.def)             Requested size: 123.60004pt x 60.48035pt.
<Figures/Mean_MIL_top_temp_Autumn_map.jpeg, id=183, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_top_temp_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_temp_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_temp_Autumn_map.jpeg  used on inp
ut line 242.
(pdftex.def)             Requested size: 105.60004pt x 60.02808pt.
<Figures/Mean_MIL_top_temp_Winter_map.jpeg, id=185, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_top_temp_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_temp_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_temp_Winter_map.jpeg  used on inp
ut line 242.
(pdftex.def)             Requested size: 105.60004pt x 60.02808pt.
<Figures/Mean_MIL_top_temp_Spring_map.jpeg, id=187, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_top_temp_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_top_temp_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_top_temp_Spring_map.jpeg  used on inp
ut line 242.
(pdftex.def)             Requested size: 130.79993pt x 58.74663pt.
<Figures/Mean_MIL_base_temp_Summer_map.jpeg, id=189, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_base_temp_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_temp_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_temp_Summer_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 123.60004pt x 55.81061pt.
<Figures/Mean_MIL_base_temp_Autumn_map.jpeg, id=191, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_base_temp_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_temp_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_temp_Autumn_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 105.60004pt x 55.39325pt.
<Figures/Mean_MIL_base_temp_Winter_map.jpeg, id=193, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_base_temp_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_temp_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_temp_Winter_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 105.60004pt x 55.39325pt.
<Figures/Mean_MIL_base_temp_Spring_map.jpeg, id=195, 635.2533pt x 341.3553pt>
File: Figures/Mean_MIL_base_temp_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_base_temp_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_base_temp_Spring_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 130.79993pt x 54.21074pt.
<Figures/Mean_MIL_temp_diff_Summer_map.jpeg, id=197, 609.7179pt x 341.3553pt>
File: Figures/Mean_MIL_temp_diff_Summer_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_temp_diff_Summer_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_temp_diff_Summer_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 123.60004pt x 74.307pt.
<Figures/Mean_MIL_temp_diff_Autumn_map.jpeg, id=199, 609.7179pt x 341.3553pt>
File: Figures/Mean_MIL_temp_diff_Autumn_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_temp_diff_Autumn_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_temp_diff_Autumn_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 105.60004pt x 73.41605pt.
<Figures/Mean_MIL_temp_diff_Winter_map.jpeg, id=201, 609.7179pt x 341.3553pt>
File: Figures/Mean_MIL_temp_diff_Winter_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_temp_diff_Winter_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_temp_diff_Winter_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 105.60004pt x 73.41605pt.
<Figures/Mean_MIL_temp_diff_Spring_map.jpeg, id=203, 609.7179pt x 341.3553pt>
File: Figures/Mean_MIL_temp_diff_Spring_map.jpeg Graphic file (type jpg)
<use Figures/Mean_MIL_temp_diff_Spring_map.jpeg>
Package pdftex.def Info: Figures/Mean_MIL_temp_diff_Spring_map.jpeg  used on in
put line 242.
(pdftex.def)             Requested size: 130.79993pt x 77.88037pt.


[16{C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/tex-gyre/q
-ts1.enc} <./Figures/Mean_MIL_top_temp_Summer_map.jpeg> <./Figures/Mean_MIL_top
_temp_Autumn_map.jpeg> <./Figures/Mean_MIL_top_temp_Winter_map.jpeg> <./Figures
/Mean_MIL_top_temp_Spring_map.jpeg> <./Figures/Mean_MIL_base_temp_Summer_map.jp
eg> <./Figures/Mean_MIL_base_temp_Autumn_map.jpeg> <./Figures/Mean_MIL_base_tem
p_Winter_map.jpeg> <./Figures/Mean_MIL_base_temp_Spring_map.jpeg> <./Figures/Me
an_MIL_temp_diff_Summer_map.jpeg> <./Figures/Mean_MIL_temp_diff_Autumn_map.jpeg
> <./Figures/Mean_MIL_temp_diff_Winter_map.jpeg> <./Figures/Mean_MIL_temp_diff_
Spring_map.jpeg>]
Underfull \vbox (badness 10000) has occurred while \output is active []



[17]
<Figures/MIL_Top_Height_Seasonal_Lines2.jpg, id=224, 1178.60326pt x 337.86224pt
>
File: Figures/MIL_Top_Height_Seasonal_Lines2.jpg Graphic file (type jpg)
<use Figures/MIL_Top_Height_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Top_Height_Seasonal_Lines2.jpg  used on in
put line 260.
(pdftex.def)             Requested size: 432.0pt x 98.92453pt.
<Figures/MIL_Base_Height_Seasonal_Lines2.jpg, id=226, 1178.60326pt x 337.86224p
t>
File: Figures/MIL_Base_Height_Seasonal_Lines2.jpg Graphic file (type jpg)
<use Figures/MIL_Base_Height_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Base_Height_Seasonal_Lines2.jpg  used on i
nput line 261.
(pdftex.def)             Requested size: 432.0pt x 98.92453pt.
<Figures/MIL_Height_Difference_Seasonal_Lines2.jpg, id=228, 1178.60326pt x 337.
86224pt>
File: Figures/MIL_Height_Difference_Seasonal_Lines2.jpg Graphic file (type jpg)

<use Figures/MIL_Height_Difference_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Height_Difference_Seasonal_Lines2.jpg  use
d on input line 262.
(pdftex.def)             Requested size: 432.0pt x 119.33289pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[18]
Underfull \vbox (badness 1629) has occurred while \output is active []



[19 <./Figures/MIL_Top_Height_Seasonal_Lines2.jpg> <./Figures/MIL_Base_Height_S
easonal_Lines2.jpg> <./Figures/MIL_Height_Difference_Seasonal_Lines2.jpg>]
<Figures/MIL_Top_Temperature_Seasonal_Lines2.jpg, id=240, 1188.23924pt x 337.86
224pt>
File: Figures/MIL_Top_Temperature_Seasonal_Lines2.jpg Graphic file (type jpg)
<use Figures/MIL_Top_Temperature_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Top_Temperature_Seasonal_Lines2.jpg  used 
on input line 280.
(pdftex.def)             Requested size: 432.0pt x 98.11606pt.
<Figures/MIL_Base_Temperature_Seasonal_Lines2.jpg, id=242, 1188.23924pt x 337.8
6224pt>
File: Figures/MIL_Base_Temperature_Seasonal_Lines2.jpg Graphic file (type jpg)
<use Figures/MIL_Base_Temperature_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Base_Temperature_Seasonal_Lines2.jpg  used
 on input line 281.
(pdftex.def)             Requested size: 432.0pt x 98.11606pt.
<Figures/MIL_Temperature_Difference_Seasonal_Lines2.jpg, id=244, 1178.60326pt x
 337.86224pt>
File: Figures/MIL_Temperature_Difference_Seasonal_Lines2.jpg Graphic file (type
 jpg)
<use Figures/MIL_Temperature_Difference_Seasonal_Lines2.jpg>
Package pdftex.def Info: Figures/MIL_Temperature_Difference_Seasonal_Lines2.jpg
  used on input line 282.
(pdftex.def)             Requested size: 432.0pt x 119.33289pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[20]
Underfull \vbox (badness 3735) has occurred while \output is active []



[21 <./Figures/MIL_Top_Temperature_Seasonal_Lines2.jpg> <./Figures/MIL_Base_Tem
perature_Seasonal_Lines2.jpg> <./Figures/MIL_Temperature_Difference_Seasonal_Li
nes2.jpg>]
<Figures/Mean_Occurrences_Seasonal_Lines_with_Errors1.jpg, id=255, 1074.1731pt 
x 754.017pt>
File: Figures/Mean_Occurrences_Seasonal_Lines_with_Errors1.jpg Graphic file (ty
pe jpg)
<use Figures/Mean_Occurrences_Seasonal_Lines_with_Errors1.jpg>
Package pdftex.def Info: Figures/Mean_Occurrences_Seasonal_Lines_with_Errors1.j
pg  used on input line 295.
(pdftex.def)             Requested size: 480.0pt x 336.9344pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[22]
Underfull \vbox (badness 10000) has occurred while \output is active []



[23 <./Figures/Mean_Occurrences_Seasonal_Lines_with_Errors1.jpg>]
Underfull \vbox (badness 2111) has occurred while \output is active []



[24]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.0pt on input line 308.

Overfull \vbox (12.36958pt too high) detected at line 330
 []

<Figures/All_Year_All_Height_Params_Spec_Plot_rev.jpeg, id=267, 1293.81367pt x 
424.31523pt>
File: Figures/All_Year_All_Height_Params_Spec_Plot_rev.jpeg Graphic file (type 
jpg)
<use Figures/All_Year_All_Height_Params_Spec_Plot_rev.jpeg>
Package pdftex.def Info: Figures/All_Year_All_Height_Params_Spec_Plot_rev.jpeg 
 used on input line 339.
(pdftex.def)             Requested size: 480.0pt x 154.89862pt.

LaTeX Warning: `!h' float specifier changed to `!ht'.



[25]
<Figures/All_Year_All_Temperature_Params_Spec_Plot_rev.jpeg, id=273, 1293.54266
pt x 425.48962pt>
File: Figures/All_Year_All_Temperature_Params_Spec_Plot_rev.jpeg Graphic file (
type jpg)
<use Figures/All_Year_All_Temperature_Params_Spec_Plot_rev.jpeg>
Package pdftex.def Info: Figures/All_Year_All_Temperature_Params_Spec_Plot_rev.
jpeg  used on input line 348.
(pdftex.def)             Requested size: 480.0pt x 155.36308pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[26 <./Figures/All_Year_All_Height_Params_Spec_Plot_rev.jpeg>]

[27 <./Figures/All_Year_All_Temperature_Params_Spec_Plot_rev.jpeg>]
Underfull \vbox (badness 10000) has occurred while \output is active []



[28]
<Figures/All_Year_Lat_Range_Trend_Spectral_Analysis_Ratio_Plot.jpeg, id=286, 14
45.4pt x 742.2129pt>
File: Figures/All_Year_Lat_Range_Trend_Spectral_Analysis_Ratio_Plot.jpeg Graphi
c file (type jpg)
<use Figures/All_Year_Lat_Range_Trend_Spectral_Analysis_Ratio_Plot.jpeg>
Package pdftex.def Info: Figures/All_Year_Lat_Range_Trend_Spectral_Analysis_Rat
io_Plot.jpeg  used on input line 368.
(pdftex.def)             Requested size: 480.0pt x 243.9871pt.


LaTeX Warning: `!h' float specifier changed to `!ht'.



[29]
Underfull \vbox (badness 10000) has occurred while \output is active []



[30 <./Figures/All_Year_Lat_Range_Trend_Spectral_Analysis_Ratio_Plot.jpeg>]
<Figures/All_Year_Trend_Spectral_Analysis_Plot.jpeg, id=295, 1293.8739pt x 425.
9112pt>
File: Figures/All_Year_Trend_Spectral_Analysis_Plot.jpeg Graphic file (type jpg
)
<use Figures/All_Year_Trend_Spectral_Analysis_Plot.jpeg>
Package pdftex.def Info: Figures/All_Year_Trend_Spectral_Analysis_Plot.jpeg  us
ed on input line 379.
(pdftex.def)             Requested size: 480.0pt x 154.8798pt.


[31 <./Figures/All_Year_Trend_Spectral_Analysis_Plot.jpeg>]
Underfull \vbox (badness 10000) has occurred while \output is active []



[32]
Underfull \vbox (badness 2343) has occurred while \output is active []



[33]
Underfull \vbox (badness 10000) has occurred while \output is active []



[34]
Underfull \vbox (badness 10000) has occurred while \output is active []



[35]
Underfull \vbox (badness 2111) has occurred while \output is active []



[36]

[37] (MIL_JAS_Paper_Review_Sep_2025.bbl

[38

]

[39]

[40]

[41]

[42]

[43]

[44]

[45])

[46] (MIL_JAS_Paper_Review_Sep_2025.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2020/03/25>
 ***********


LaTeX Font Warning: Size substitutions with differences
(Font)              up to 0.45pt have occurred.

 ) 
Here is how much of TeX's memory you used:
 18978 strings out of 473904
 400536 string characters out of 5724713
 1956908 words of memory out of 5000000
 41520 multiletter control sequences out of 15000+600000
 621466 words of font info for 175 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 95i,20n,107p,3298b,850s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/newtx/Ne
wTXMI.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/
newtx/NewTXMI7.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type
1/public/tex-gyre/qtmr.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fo
nts/type1/public/newtx/txmiaX.pfb><C:/Users/<USER>/AppData/Local/Programs/Mi
KTeX/fonts/type1/public/newtx/txsys.pfb><C:/Users/<USER>/AppData/Local/Progr
ams/MiKTeX/fonts/type1/public/txfonts/txtt.pfb><C:/Users/<USER>/AppData/Loca
l/Programs/MiKTeX/fonts/type1/urw/helvetic/uhvr8a.pfb><C:/Users/<USER>/AppDa
ta/Local/Programs/MiKTeX/fonts/type1/public/newtx/ztmb.pfb><C:/Users/<USER>/
AppData/Local/Programs/MiKTeX/fonts/type1/public/newtx/ztmr.pfb><C:/Users/<USER>
unde/AppData/Local/Programs/MiKTeX/fonts/type1/public/newtx/ztmri.pfb>
Output written on MIL_JAS_Paper_Review_Sep_2025.pdf (46 pages, 49334713 bytes).

PDF statistics:
 399 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 435 words of extra memory for PDF output out of 10000 (max. 10000000)

