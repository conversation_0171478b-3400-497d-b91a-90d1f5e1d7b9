This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: Reviewer_Response_Reviewer_2.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: plainnat.bst
Reallocating 'name_of_file' (item size: 1) to 12 items.
Database file #1: MIL_JAS.bib
You're missing a field name---line 2337 of file MIL_JAS.bib
 :  year      = {Accessed 2023-2024}, 
 :                                    % Or the year you accessed it
I'm skipping whatever remains of this entry
You're missing a field name---line 2349 of file MIL_JAS.bib
 :  author    = {{Python Software Foundation}}, 
 :                                              % Or a more specific citation if van1995python refers to a particular library/paper
I'm skipping whatever remains of this entry
You've used 1 entry,
            2773 wiz_defined-function locations,
            633 strings with 5594 characters,
and the built_in function-call counts, 694 in all, are:
= -- 57
> -- 48
< -- 0
+ -- 16
- -- 15
* -- 69
:= -- 113
add.period$ -- 4
call.type$ -- 1
change.case$ -- 8
chr.to.int$ -- 1
cite$ -- 2
duplicate$ -- 21
empty$ -- 48
format.name$ -- 16
if$ -- 136
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 1
newline$ -- 14
num.names$ -- 4
pop$ -- 13
preamble$ -- 1
purify$ -- 7
quote$ -- 0
skip$ -- 18
stack$ -- 0
substring$ -- 41
swap$ -- 1
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 11
warning$ -- 0
while$ -- 4
width$ -- 0
write$ -- 22
(There were 2 error messages)
