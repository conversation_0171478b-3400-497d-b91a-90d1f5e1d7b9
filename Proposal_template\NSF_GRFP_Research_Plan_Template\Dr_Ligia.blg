This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: Dr_Ligia.aux
The style file: apalike.bst
Database file #1: Dr_Ligia.bib
You've used 6 entries,
            1935 wiz_defined-function locations,
            545 strings with 5765 characters,
and the built_in function-call counts, 3177 in all, are:
= -- 294
> -- 183
< -- 2
+ -- 66
- -- 66
* -- 305
:= -- 552
add.period$ -- 18
call.type$ -- 6
change.case$ -- 63
chr.to.int$ -- 6
cite$ -- 6
duplicate$ -- 93
empty$ -- 194
format.name$ -- 74
if$ -- 608
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 6
newline$ -- 33
num.names$ -- 18
pop$ -- 52
preamble$ -- 1
purify$ -- 63
quote$ -- 0
skip$ -- 74
stack$ -- 0
substring$ -- 240
swap$ -- 15
text.length$ -- 1
text.prefix$ -- 0
top$ -- 0
type$ -- 36
warning$ -- 0
while$ -- 21
width$ -- 0
write$ -- 80
