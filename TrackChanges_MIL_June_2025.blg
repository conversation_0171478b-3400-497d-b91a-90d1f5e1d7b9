This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: TrackChanges_MIL_June_2025.aux
Reallocating 'name_of_file' (item size: 1) to 10 items.
The style file: ametsocV6.bst
Reallocating 'name_of_file' (item size: 1) to 12 items.
Database file #1: MIL_JAS.bib
Repeated entry---line 831 of file MIL_JAS.bib
 : @article{fritts2018
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 995 of file MIL_JAS.bib
 : @article{salby2002
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 1423 of file MIL_JAS.bib
 : @article{leblanc1997
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 2023 of file MIL_JAS.bib
 : @inproceedings{begue2017
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 2201 of file MIL_JAS.bib
 : @article{liu1998
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2221 of file MIL_JAS.bib
 : @article{lindzen1981
 :                     ,
I'm skipping whatever remains of this entry
You're missing a field name---line 2337 of file MIL_JAS.bib
 :  year      = {Accessed 2023-2024}, 
 :                                    % Or the year you accessed it
I'm skipping whatever remains of this entry
Repeated entry---line 2347 of file MIL_JAS.bib
 : @manual{van1995python
 :                      ,
I'm skipping whatever remains of this entry
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Warning--can't use both volume and number fields in begue2017
Warning--empty booktitle in fadnavis2004
Warning--can't use both volume and number fields in fadnavis2004
Warning--can't use both volume and number fields in huang2006
Warning--can't use both volume and number fields in huang2016
Warning--can't use both volume and number fields in sridharan2008
You've used 77 entries,
            3491 wiz_defined-function locations,
            1216 strings with 24533 characters,
and the built_in function-call counts, 90326 in all, are:
= -- 10391
> -- 3744
< -- 24
+ -- 2124
- -- 939
* -- 5058
:= -- 9340
add.period$ -- 154
call.type$ -- 77
change.case$ -- 688
chr.to.int$ -- 76
cite$ -- 83
duplicate$ -- 8874
empty$ -- 4480
format.name$ -- 1118
if$ -- 18402
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 924
newline$ -- 242
num.names$ -- 308
pop$ -- 3809
preamble$ -- 1
purify$ -- 612
quote$ -- 0
skip$ -- 5376
stack$ -- 0
substring$ -- 4066
swap$ -- 7368
text.length$ -- 7
text.prefix$ -- 0
top$ -- 0
type$ -- 690
warning$ -- 6
while$ -- 406
width$ -- 0
write$ -- 936
(There were 8 error messages)
