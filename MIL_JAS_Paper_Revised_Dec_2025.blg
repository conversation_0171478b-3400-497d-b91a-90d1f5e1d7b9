This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MIL_JAS_Paper_Revised_Dec_2025.aux
Reallocating 'name_of_file' (item size: 1) to 10 items.
The style file: ametsocV6.bst
Reallocating 'name_of_file' (item size: 1) to 12 items.
Database file #1: MIL_JAS.bib
Repeated entry---line 831 of file MIL_JAS.bib
 : @article{fritts2018
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 995 of file MIL_JAS.bib
 : @article{salby2002
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 1423 of file MIL_JAS.bib
 : @article{leblanc1997
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 2023 of file MIL_JAS.bib
 : @inproceedings{begue2017
 :                         ,
I'm skipping whatever remains of this entry
Repeated entry---line 2201 of file MIL_JAS.bib
 : @article{liu1998
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 2221 of file MIL_JAS.bib
 : @article{lindzen1981
 :                     ,
I'm skipping whatever remains of this entry
You're missing a field name---line 2337 of file MIL_JAS.bib
 :  year      = {Accessed 2023-2024}, 
 :                                    % Or the year you accessed it
I'm skipping whatever remains of this entry
Repeated entry---line 2347 of file MIL_JAS.bib
 : @manual{van1995python
 :                      ,
I'm skipping whatever remains of this entry
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Warning--can't use both volume and number fields in begue2017
Warning--empty booktitle in fadnavis2004
Warning--can't use both volume and number fields in fadnavis2004
Warning--can't use both volume and number fields in huang2006
Warning--can't use both volume and number fields in sridharan2008
You've used 71 entries,
            3491 wiz_defined-function locations,
            1180 strings with 23110 characters,
and the built_in function-call counts, 81925 in all, are:
= -- 9362
> -- 3432
< -- 22
+ -- 1934
- -- 861
* -- 4614
:= -- 8546
add.period$ -- 142
call.type$ -- 71
change.case$ -- 632
chr.to.int$ -- 70
cite$ -- 76
duplicate$ -- 8016
empty$ -- 4087
format.name$ -- 1026
if$ -- 16689
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 848
newline$ -- 224
num.names$ -- 284
pop$ -- 3437
preamble$ -- 1
purify$ -- 562
quote$ -- 0
skip$ -- 4843
stack$ -- 0
substring$ -- 3651
swap$ -- 6613
text.length$ -- 6
text.prefix$ -- 0
top$ -- 0
type$ -- 636
warning$ -- 5
while$ -- 369
width$ -- 0
write$ -- 863
(There were 8 error messages)
