%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% APARC Gravity Waves Symposium Poster - Award-Winning Design
% A0 Portrait Format (84.1 cm x 118.9 cm)
% Created for presentation at APARC Gravity Waves and Fine vertical-Scale
% Atmospheric Processes and Structures Symposium, Seoul, South Korea, 9-13 Jun 2025
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\documentclass[final]{beamer} % Added 'demo' option for missing images

%-------------------------------------------------------------------------------
%   PACKAGES
%-------------------------------------------------------------------------------
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{lmodern} % Changed font
\usepackage{graphicx} % graphicx is loaded by beamer; 'demo' option passed via \documentclass
\usepackage{amsmath,amssymb,amsfonts,amsthm}
\usepackage{booktabs}
\usepackage{tikz}
\usepackage{xcolor}
% \usepackage{multicol} % Not used, beamer columns are used instead
\usepackage{siunitx}
% \usepackage{hyperref} % Beamer loads this automatically
\usepackage[font=scriptsize,labelfont=bf]{caption} % Set global caption font to scriptsize\usepackage{caption}

% Option 1: Remove labels for ALL figures
\captionsetup[figure]{labelformat=empty}
\usepackage{array}
% \usepackage{tabularray} % Not used, can be removed if not planned for future
\usepackage{ragged2e}
\usepackage{enumitem}
\usepackage{etoolbox}
\usepackage{diagbox}
\usepackage{multirow}
\usepackage{qrcode}

\newcommand{\sci}[2]{{#1} \times 10^{#2}} % Helper command for scientific notation
%-------------------------------------------------------------------------------
%   BEAMERPOSTER SETTINGS
%-------------------------------------------------------------------------------
% Set poster size to A0 portrait with optimal scaling
\usepackage[size=a0,orientation=portrait,scale=1.35]{beamerposter}

\graphicspath{ 
	{.} % document root dir
	{Figures/}	
	{Samples/}			
}

%-------------------------------------------------------------------------------
%   THEME & COLORS
%-------------------------------------------------------------------------------
% Define custom colors for a professional, award-winning look
\definecolor{primarycolor}{RGB}{0, 51, 102}   % Deep navy blue
\definecolor{secondarycolor}{RGB}{0, 102, 153}  % Medium blue
\definecolor{accentcolor}{RGB}{204, 102, 0}   % Burnt orange
\definecolor{lightbg}{RGB}{240, 247, 255}   % Light blue background
\definecolor{headerbg}{RGB}{230, 240, 250}   % Header background
\definecolor{bordercolor}{RGB}{0, 71, 122}   % Border color
\definecolor{highlightcolor}{RGB}{255, 240, 220} % Highlight background

% Set theme
\usetheme{default}
\usecolortheme{default}
\useinnertheme{default}
\useoutertheme{default}

% Adjust spacing for better content distribution
\setlength{\columnsep}{1.8em}
\setbeamersize{text margin left=1.5cm, text margin right=1.5cm}

%-------------------------------------------------------------------------------
%   PAGE FRAME & BACKGROUND
%-------------------------------------------------------------------------------
% Add page frame with TikZ
\usetikzlibrary{calc,positioning,matrix,shapes.geometric,decorations.pathmorphing}

% Define a background template with a border and subtle design elements
\setbeamertemplate{background}{
	\begin{tikzpicture}[remember picture,overlay]
		% Main border
		\draw[bordercolor, line width=4pt, rounded corners=2mm]
		($(current page.north west)+(0.5cm,-0.5cm)$) rectangle
		($(current page.south east)+(-0.5cm,0.5cm)$);
		
		% Decorative corner elements
		\foreach \corner in {north west, north east, south west, south east} {
			\fill[accentcolor, opacity=0.7]
			($(current page.\corner)+(1cm,-1cm)$) circle (0.5cm); % Example: adjust position if needed
		}
		
		% Subtle background pattern
		\foreach \i in {1,...,10} {
			\draw[primarycolor, opacity=0.03, line width=2pt,
			decoration={random steps, segment length=3cm, amplitude=1cm},
			decorate]
			($(current page.north west)+(5cm,-\i*10cm)$) --
			($(current page.north east)+(-5cm,-\i*10cm)$);
		}
	\end{tikzpicture}
}

%-------------------------------------------------------------------------------
%   HEADER & FOOTER
%-------------------------------------------------------------------------------
%-------------------------------------------------------------------------------
%   HEADER & FOOTER
%-------------------------------------------------------------------------------
% Customize beamer template
\setbeamertemplate{navigation symbols}{}
% \setbeamertemplate{caption}[numbered] % Already handled by caption package options

% Header with logo spaces
\setbeamertemplate{headline}{
	\leavevmode
	% CONFERENCE BAR AT THE VERY TOP
	\begin{beamercolorbox}[wd=\paperwidth, colsep=0.5cm, ht=3cm, center]{headerbg} % Adjust ht as needed (e.g., 1.5cm or 2cm)
		\usebeamercolor{conference in headline}{\color{accentcolor}\normalsize{\textbf{APARC Gravity Waves and Fine vertical-Scale Atmospheric Processes and Structures Symposium, Seoul, South Korea, 9-13 June 2025}}}
	\end{beamercolorbox}
	% \vspace{0.1cm} % Optional: small vertical space between conference bar and main header content below
	
	% MAIN HEADER BOX (logos, title, authors, etc.)
	\begin{beamercolorbox}[wd=\paperwidth,colsep=0cm,ht=6cm]{headerbg} % This is the original 10cm box
		\begin{columns}
			\begin{column}{0.15\linewidth}
				% Left logo space
				\vskip1cm % Space from the top of this beamercolorbox
				\centering
				% Consider adjusting image height to fit within 10cm box minus vskip
				\includegraphics[height=8cm, keepaspectratio=true]{Assinatura2023.png}
			\end{column}
			\begin{column}{0.7\linewidth}
			% Content starts after some vertical skip.
			% The original had \vskip0.5cm, then conference, then \vskip1cm, then title.
			% Now that conference is removed, we can adjust.
			% Let's make the title start 1cm from the top of this box for consistency
			% with logos, or adjust as needed for visual balance.
			\vskip1cm 
			\centering % To center the block of title, authors, institute
			\usebeamercolor{title in headline}{\color{primarycolor}\fontsize{47}{55}\selectfont\textbf{Near-global Occurrences of Mesospheric Inversion Layers Observed from 22}}
			\vskip0.2cm
			\usebeamercolor{title in headline}{\color{primarycolor}\fontsize{47}{55}\selectfont\textbf{years of TIMED/SABER Temperature Measurements}}
			\vskip0.7cm
			\usebeamercolor{author in headline}{\color{secondarycolor}\normalsize{\textbf{Toyese Tunde Ayorinde$^{1}$, \textbf{Cristiano Max Wrasse}$^{1}$, Ebenezer Agyei-Yeboah$^{2}$, Hisao Takahashi$^{1}$, Cosme Alexandre Oliveira Barros Figueiredo$^{3}$, Diego Barros$^{1}$, Anderson Vestena Bilibio$^{1}$, Luiz Fillip Rodrigues Vital$^{1}$}}}
			\vskip0.5cm
			\usebeamercolor{institute in headline}{\color{secondarycolor}\small{%
					$^{1}$Divisão de Clima Espacial, Instituto Nacional de Pesquisas Espacial (INPE), S\~{a}o Jos\'{e} dos Campos, Brazil\\
					$^{2}$Laboratorio de Física e Astronomia, Universidade do Vale do Paraíba, São José dos Campos, Brazil\\
					$^{3}$Unidade Acadêmica de Física, Universidade Federal de Campina Grande, Campina Grande, PB, Brazil
			}}
		\end{column}
		\begin{column}{0.15\linewidth}
			% Right logo space
			\vskip1cm % Space from the top of this beamercolorbox
			\centering
			% Consider adjusting image height
			\includegraphics[height=7cm, keepaspectratio=true]{Aparc.jpg}
		\end{column}
	\end{columns}
\end{beamercolorbox}
\vspace{-1cm} % Space before the separator line
\begin{beamercolorbox}[wd=\paperwidth,colsep=0.5cm]{lower separation line head}
	\rule{0pt}{3pt}
\end{beamercolorbox}
}

% Footer with contact info and QR code
\setbeamertemplate{footline}{
	\vspace{1cm} % Reduced space between poster content and footer to ensure it fits
	\begin{beamercolorbox}[wd=\paperwidth,colsep=0.5cm]{upper separation line foot}
		\rule{0pt}{3pt}
	\end{beamercolorbox}
	\leavevmode
	\begin{beamercolorbox}[wd=\paperwidth,colsep=0cm,ht=4cm]{headerbg} % Box height is 4cm
		\begin{columns}
			\begin{column}{0.96\linewidth} % CORRECTED TYPO HERE
				\vspace{0pt plus 1fill} % CORRECTED TYPO HERE (extra 'l' removed) For vertical centering of content below
				\begin{center}
					\usebeamercolor{author in headline}{\color{primarycolor}\normalsize{Contact: \texttt{<EMAIL>} \hfill \textbf{Scan for more information:} \qrcode[height=1.8cm]{mailto:<EMAIL>} \hfill \textbf{APARC Gravity Waves Symposium 2025}}} % Reduced font size and QR code height
				\end{center}
				\vspace{0pt plus 1fill} % CORRECTED TYPO HERE (extra 'l' removed) For vertical centering
			\end{column}
		\end{columns}
	\end{beamercolorbox}
}

%-------------------------------------------------------------------------------
%   BLOCK STYLES
%-------------------------------------------------------------------------------
% Block styles
\setbeamertemplate{block begin}{
	\vskip.5ex
	\begin{beamercolorbox}[rounded=true,shadow=true,leftskip=1cm,colsep*=.85ex]{block title}%
		\usebeamerfont*{block title}\insertblocktitle
	\end{beamercolorbox}%
	{\ifbeamercolorempty[bg]{block body}{}{\nointerlineskip\vskip-0.5pt}}%
	\usebeamerfont{block body}%
	\begin{beamercolorbox}[rounded=true,shadow=true,colsep*=.85ex,sep=.85ex,vmode]{block body}%
		\ifbeamercolorempty[bg]{block body}{\vskip-.25ex}{\vskip-.75ex}\vbox{}%
	}
	\setbeamertemplate{block end}{
	\end{beamercolorbox}
}

% Define a custom block style for highlighted content
\newenvironment{highlightblock}[1]{%
	\setbeamercolor{block title}{bg=accentcolor,fg=white}%
	\setbeamercolor{block body}{bg=highlightcolor,fg=black}%
	\begin{block}{#1}}{\end{block}%
	\setbeamercolor{block title}{bg=primarycolor,fg=white}%
	\setbeamercolor{block body}{bg=lightbg,fg=black}%
}

% Set colors for blocks
\setbeamercolor{block title}{bg=primarycolor,fg=white}
\setbeamercolor{block body}{bg=lightbg,fg=black}

% Title formatting
\setbeamerfont{block title}{size=\Large,series=\bfseries} % Kept Large, adjust to \large if needed
\setbeamerfont{block body}{size=\normalsize} % Reduced from \large

% Adjust itemize and enumerate environments
\setbeamertemplate{itemize item}{\color{primarycolor}$\blacktriangleright$}
\setbeamertemplate{itemize subitem}{\color{secondarycolor}$\blacktriangleright$}
\setbeamertemplate{enumerate item}{\insertenumlabel.}
\setbeamertemplate{enumerate subitem}{\insertenumlabel.\insertsubenumlabel}

% Adjust spacing in lists
\setlist[itemize]{topsep=0.5ex,itemsep=0.5ex,parsep=0.5ex,leftmargin=*}
\setlist[enumerate]{topsep=0.5ex,itemsep=0.5ex,parsep=0.5ex,leftmargin=*}

%-------------------------------------------------------------------------------
%   DOCUMENT START
%-------------------------------------------------------------------------------
\begin{document}
	
	\begin{frame}[t]
		\begin{columns}[t]
			\begin{column}{0.01\textwidth}
			\end{column}
			
			%===========================================================================
			%   COLUMN 1
			%===========================================================================
			\begin{column}{0.32\textwidth}
				
				\begin{highlightblock}{\small{Abstract}}
					\justifying \footnotesize % Using small for abstract to ensure it fits well
					The long-term occurrence of Mesospheric Inversion Layers (MILs) was analyzed using 22 years (2002–2023) of SABER temperature data. We examined MIL occurrence near-globally, monthly, and latitudinally, applying multi-linear regression (MLR) to assess trends and responses to El Ni\~no–Southern Oscillation (ENSO), quasi-biennial oscillation (QBO), and solar flux ($F_{10.7cm}$). MIL parameters (top/base heights and temperatures, height/temperature differences) exhibit clear hemispherical asymmetry.  In general, MIL occurrences peak during equinoxes and decline during solstices. Latitudinally, tropical regions (\ang{30}N-\ang{30}S) show the highest MIL occurrences during equinoxes and the lowest during solstices. In mid-latitudes and polar regions (\ang{30}-\ang{83}N/S), MILs peak in autumn and winter, with a minimum in spring and summer. Periodicities in MIL occurrences vary near-globally and by latitude. The tropics feature the smallest mean thickness difference ($\sim$\num{0.61} \si{\km}) but the largest mean temperature difference ($\sim$\num{23.72} \si{\kelvin}).  The latitudinal patterns may reflect seasonal variations in dynamics that have a stronger influence on temperature inversions than on the vertical distributions in the mesosphere. Over 22 years, MLR revealed a near-global MIL occurrence increase of $\sim$\num{0.42}$\pm$\num{0.22}\% per decade ($\sim$\num{0.042}$\pm$\num{0.022}\% per year), with the 11-year solar cycle exerting significant control. ENSO and QBO modulate MIL occurrences negatively and positively, respectively, reflecting their combined impact on mesosphere dynamics.
				\end{highlightblock}
				
				\begin{block}{\small{Introduction}}
					\RaggedRight\small
					\begin{itemize}[leftmargin=*, nosep]
						\item The mesosphere (50-100 km) is a transition zone influenced by atmospheric waves (GWs, tides, PWs).
						\item Mesospheric Inversion Layers (MILs) are characterized by a localized reversal in the vertical temperature gradient within the mesosphere.
						\item Typically occur at altitudes between 70 km and 95 km with a thickness of 5 to 10 km.
						\item Temperature increase can often be 30-50 K higher than the cooler surrounding mesosphere.
						\item MILs are linked to the presence of gravity waves (GWs) and their interactions with mean flow and atmospheric tides.
						\item Long-term observations show MILs exhibit substantial variability on both seasonal and interannual timescales.
						\item \textbf{Objective:} Climatological understanding of MILs using 22 years of TIMED/SABER data, and evaluating impacts of solar activity, ENSO, and QBO.
					\end{itemize}

				\end{block}
				\begin{block}{\small{Mechanisms \& Physical Processes}}
					\RaggedRight\scriptsize
						\textbf{Key Mechanisms:}
						\begin{itemize}[leftmargin=*, nosep]
								\item Gravity wave breaking and dissipation
								\item Planetary wave-mean flow interactions
								\item Tidal influences and modulation
								\item Chemical heating processes
								\item Radiative effects
							\end{itemize}
				\end{block}
				
				\begin{block}{\small{Data \& Methodology}}
					\RaggedRight\small
					\textbf{Data:}
					\begin{itemize}[leftmargin=*, nosep]
						\item TIMED/SABER Level 2A vertical temperature profiles (Jan 2002 - Dec 2023)
						\item Near-global coverage: \ang{52}S-\ang{83}N (yaw cycle). Resolution: $\sim$2 km vertical
						\item Accuracy: 1.5 K (15-80 km), 4 K (80-100 km)
					\end{itemize}
					
					\begin{minipage}{0.48\textwidth}
						\textbf{MIL Identification Criteria:}
						\begin{itemize}[leftmargin=*, nosep]
							\item Base at least 5 km above stratopause
							\item Top below 90 km altitude
							\item Temperature difference: 10-100 K
							\item Thickness greater than 4 km
						\end{itemize}
					\end{minipage}
					\hfill
					\begin{minipage}{0.48\textwidth}
						\begin{figure}[h!]		
							\begin{center} 				
								\begin{tabular}{ccc}
									\includegraphics[width=0.45\textwidth, height=0.14\textheight, keepaspectratio=true,  trim=5 30 25 60, clip]{Temp_Profile_SABER_2003_001_05.jpeg}&\hspace*{-3mm}
									\includegraphics[width=0.45\textwidth, height=0.14\textheight, keepaspectratio=true,  trim=30 30 25 60, clip]{Temp_Profile_SABER_2003_001_00.jpeg}\\
								\end{tabular}			
							\end{center}	
							\caption{\textbf{Fig 1:} Examples of temperature profiles with an MIL and a profile without a MIL, showing the stratopause (red star), MIL base (magenta star), and MIL top (cyan star) locations. \textbf{(a)} is a MIL temperature profile. \textbf{(b)} is a non-MIL temperature profile. }
							\label{fig:Met1}
						\end{figure}
					\end{minipage}
					
					\RaggedRight\small
					\begin{figure}
						\centering
						\includegraphics[width=0.95\textwidth, height=0.085\textheight, keepaspectratio=false]{All_Profile_MIL_Counts_with_Trends.jpeg}% Reduced size
						\caption{\textbf{Fig 2:} Time series and regression analysis of MIL occurrences from 2002 to 2023. (a) Monthly profiles from TIMED/SABER satellite (blue), and Monthly occurrences of MILs (red).}
						\label{fig:wavelet2}
					\end{figure}
					
					\vspace{0.5cm}
%					\begin{minipage}{0.48\textwidth}
%						\centering
%						\includegraphics[width=\textwidth, height=0.13\textheight, keepaspectratio]{TIMED_SABER_satellite.jpg}
%						\captionof{figure}{TIMED/SABER satellite.}
%					\end{minipage}
%					\hfill
%					\begin{minipage}{0.48\textwidth}
%						\centering
%						\includegraphics[width=\textwidth, height=0.13\textheight, keepaspectratio]{SABER_coverage_map.jpg}
%						\captionof{figure}{SABER global coverage.}
%					\end{minipage}
					
					\vspace{0.3cm}
					\textbf{Multi-Linear Regression (MLR):}
					\small Monthly MIL occurrences vs. F$_{10.7cm}$, QBO (30mb, 50mb), ENSO (MEI).
				\end{block}
				
			\end{column}
			
			%===========================================================================
			%   COLUMN 2
			%===========================================================================
			\begin{column}{0.32\textwidth}
				
				\begin{block}{\small{Seasonal \& Latitudinal MIL Occurrences}}
					\RaggedRight\small
					\begin{itemize}[leftmargin=*, nosep]
						\item \textbf{Global:} Highest in SON ($\sim$40k) \& MAM ($\sim$38k), lowest in DJF ($<$30k).
						\item \textbf{Tropics (30°N-30°S):} Similar to global mean.
						\item \textbf{Mid-latitudes (30°-60°N/S):} Peak in winter, lowest in summer.
						\item \textbf{Polar (60°-83°N/S):} NH: Peak DJF (winter), low JJA. SH: Less seasonal variation.
					\end{itemize}
					
					\begin{center}
						\begin{figure}[h!]
						\includegraphics[width=0.95\textwidth, height=0.1\textheight, keepaspectratio]{seasonal_grouped_bar_plot.jpg}
						\caption{\textbf{Fig 3:} Seasonal and Latitudinal Occurrence of MILs in different latitude bands (\ang{60}–\ang{83}N, \ang{30}–\ang{60}N, \ang{30}N–\ang{30}S, \ang{30}–\ang{60}S, and \ang{60}–\ang{83}S) from 2002 to 2023. Bars represent total MIL occurrences (thousands) for each season: DJF (blue), MAM (green), JJA (red), and SON (orange).}
						\end{figure}
					\end{center}
				\end{block}
				
				\begin{block}{\small{Global Mean MIL Parameters}}
					\RaggedRight\small
					\textbf{MIL Top Height (TH) \& Base Height (BH):}
					\begin{itemize}[leftmargin=*, nosep]
						\item Distinct hemispheric asymmetry. SH higher THs (85-95 km) in DJF.
						\item Max TH near summer poles ($>$85km). Equatorial TH 80-85km.
						\item BH structure similar to TH. DJF: SH $\sim$80km, NH $\sim$70km.
					\end{itemize}
					
					\begin{center}
						\begin{figure}[h!]		
							\begin{center}
								\centerline{  					
									\begin{tabular}{cccc}
										\includegraphics[width=0.5\textwidth, height=0.07\textheight, keepaspectratio=true,  trim=10 89 90 5, clip]{Mean_MIL_top_height_Summer_map.jpeg}&\hspace*{-5mm}				
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 89 5 5, clip]{Mean_MIL_top_height_Winter_map.jpeg}\\				
										\includegraphics[width=0.5\textwidth, height=0.065\textheight, keepaspectratio=true,  trim=10 89 90 24, clip]{Mean_MIL_base_height_Summer_map.jpeg}&\hspace*{-5mm}				
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 89 5 24, clip]{Mean_MIL_base_height_Winter_map.jpeg}\\
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=10 5 100 24, clip]{Mean_MIL_height_diff_Summer_map.jpeg}&\hspace*{-5mm}
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 5 5 24, clip]{Mean_MIL_height_diff_Winter_map.jpeg}\\
									\end{tabular}
								}				
							\end{center}	
							\caption{\textbf{Fig 4:} Global seasonal mean MIL top heights (top row), base heights (middle row), and thickness (bottom row) for different seasons (DJF, MAM, JJA, SON).}
						\end{figure}						
					\end{center}
					
					\textbf{MIL Thickness:}
					\begin{itemize}[leftmargin=*, nosep]
						\item High in tropics ($\pm10^\circ$) during MAM/SON ($>5.5$ km).
						\item Elevated above $30^\circ$ NH (DJF) \& SH (JJA).
						\item Lowest near polar regions during JJA (NH) \& DJF (SH) ($<5$ km).
					\end{itemize}
				\end{block}
				
				\begin{block}{\small{Latitudinal Distribution of MIL Occurrences}}
					\RaggedRight\small % Added \small for consistency
					\begin{minipage}{0.48\textwidth}
					\begin{figure}[h!]		
						\begin{center} 				
							\begin{tabular}{ccc}
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 65 0 5, clip]{MIL_Top_Height_Seasonal_Lines2.jpg}\\
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 65 0 5, clip]{MIL_Base_Height_Seasonal_Lines2.jpg}\\
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 10 0 5, clip]{MIL_Height_Difference_Seasonal_Lines2.jpg}\\
							\end{tabular}				
						\end{center}	
						\caption{\textbf{Fig 5:} The seasonally averaged MIL top, BHs, and thickness at different latitudinal ranges.}
					\end{figure}
				\end{minipage}
					\hfill
				\begin{minipage}{0.48\textwidth}
					\begin{figure}[h!]		
						\begin{center} 				
							\begin{tabular}{ccc}
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 65 0 5, clip]{MIL_Top_Temperature_Seasonal_Lines2.jpg}\\
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 65 0 5, clip]{MIL_Base_Temperature_Seasonal_Lines2.jpg}\\
								\includegraphics[width=\textwidth, height=0.1\textheight, keepaspectratio=true,  trim=5 10 0 5, clip]{MIL_Temperature_Difference_Seasonal_Lines2.jpg}\\
							\end{tabular}				
						\end{center}	
						\caption{\textbf{Fig 6:} The seasonally averaged MIL top, BHs, and thickness at different latitudinal ranges.}
					\end{figure}
				\end{minipage}
%					\begin{center}
%						\includegraphics[width=0.95\textwidth, height=0.1\textheight, keepaspectratio]{MIL_Top_Height_Seasonal_Lines2.jpg}
%						\captionof{figure}{Latitudinal distribution of MIL occurrences showing seasonal variations across different latitude bands. Tropical regions show prominent peaks during MAM and SON, while mid-latitudes show highest occurrences during winter in both hemispheres.}
%					\end{center}
					
					\begin{minipage}{0.48\textwidth}
						\begin{figure}[h!]	
						\centering
						\includegraphics[width=\textwidth, height=0.12\textheight, keepaspectratio=false]{MIL_Altitude_Distribution.jpg}
						\caption{\textbf{Fig 7:} Altitude distribution of MILs by latitude.}
						\end{figure}
					\end{minipage}
					\hfill
					\begin{minipage}{0.48\textwidth}
						\begin{figure}[h!]	
						\centering
						\includegraphics[width=\textwidth, height=0.12\textheight, keepaspectratio]{MIL_Monthly_Latitudinal_Variation2.jpg}
						\caption{\textbf{Fig 8:} Monthly variation of MIL occurrences.}
						\end{figure}
					\end{minipage}
				\end{block}
				
			\end{column}
			
			%===========================================================================
			%   COLUMN 3
			%===========================================================================
			\begin{column}{0.32\textwidth}
				
				\begin{block}{\small{Temperature Characteristics of MILs}}
					\RaggedRight\small					
					\begin{center}
						\begin{figure}[h!]		
							\begin{center}
								\centerline{  					
									\begin{tabular}{cccc}
										\includegraphics[width=0.5\textwidth, height=0.06\textheight, keepaspectratio=true,  trim=10 89 110 5, clip]{Mean_MIL_top_temp_Summer_map.jpeg}&\hspace*{-5mm}				
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 89 5 5, clip]{Mean_MIL_top_temp_Winter_map.jpeg}\\				
										\includegraphics[width=0.5\textwidth, height=0.055\textheight, keepaspectratio=true,  trim=10 89 110 24, clip]{Mean_MIL_base_temp_Summer_map.jpeg}&\hspace*{-5mm}				
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 89 5 24, clip]{Mean_MIL_base_temp_Winter_map.jpeg}\\
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=10 5 85 24, clip]{Mean_MIL_temp_diff_Summer_map.jpeg}&\hspace*{-5mm}
										\includegraphics[width=0.5\textwidth, height=0.08\textheight, keepaspectratio=true,  trim=80 5 5 24, clip]{Mean_MIL_temp_diff_Winter_map.jpeg}\\
									\end{tabular}
								}				
							\end{center}	
							\caption{\textbf{Fig 9:} Near-global means of top temperatures (top row), base temperatures (middle row), and temperature differences (bottom row) of MIL occurrences for different seasons (DJF, JJA).}
						\end{figure}						
					\end{center}
					\small{
					\begin{itemize}[leftmargin=*, nosep]
						\item \textbf{MIL Top Temperature:}
						\begin{itemize}
							\item Equatorial to subtropical regions: High values ($\sim$220-250 K).
							\item Polar regions: Significantly colder (160-170 K).
						\end{itemize}
						\item \textbf{MIL Base Temperature:}
						\begin{itemize}
							\item Equatorial regions: Warmer (225-230 K).
							\item Mid-latitudes: Moderate (170-210 K).
							\item Polar regions: Cooler (<170 K).
						\end{itemize}
						\item \textbf{MIL Temperature Difference:}
						\begin{itemize}
							\item Equatorial regions: Highest (50-60 K).
							\item Mid-latitudes: Moderate (25-40 K).
							\item Polar regions: Lowest (25-30 K).
						\end{itemize}
					\end{itemize}
				            }
		            \begin{center}
		            	
		            	\begin{table}[htbp]
		            		\centering
		            		\resizebox{\textwidth}{!}{ % Adjust to fit the table within the text width
		            		\fontsize{9}{8}\selectfont % Extremely small font size (4pt with 6pt line spacing)
		            			\begin{tabular}{|>{\bfseries}p{2.6cm}|p{1.4cm}|p{1.4cm}|p{1.4cm}|p{1.4cm}|p{1.7cm}|p{1.4cm}|p{1.4cm}|p{1.4cm}|p{1.4cm}|p{1.4cm}|}
		            				\hline
		            				\multirow{2}{*}{\diagbox[width=3cm, height=0.6cm]{}{}} & \multicolumn{2}{c|}{\textbf{\ang{60}-\ang{83}N}} & \multicolumn{2}{c|}{\textbf{\ang{30}-\ang{60}N}} & \multicolumn{2}{c|}{\textbf{\ang{30}N-\ang{30}S}} & \multicolumn{2}{c|}{\textbf{\ang{30}-\ang{60}S}} & \multicolumn{2}{c|}{\textbf{\ang{60}-\ang{83}S}} \\
		            				\cline{2-11}
		            				& \textbf{Periodicity} & \textbf{Total diff} & \textbf{Periodicity} & \textbf{Total diff} & \textbf{Periodicity} & \textbf{Total diff} & \textbf{Periodicity} & \textbf{Total diff} & \textbf{Periodicity} & \textbf{Total diff} \\
		            				\hline
		            				Top Height (\si{\km}) & \textit{Annual} & 12.74 & \textit{Annual} & 11.42 & \textit{Semiannual},\newline \textit{Annual} & 3.7 & \textit{Annual} & 9.91 & \textit{Annual} & 13.25 \\
		            				\hline
		            				Base Height (\si{\km}) & \textit{Annual} & 13.66 & \textit{Annual} & 11.92 & \textit{Semiannual},\newline \textit{Annual} & 3.43 & \textit{Annual} & 10.26 & \textit{Annual} & 13.72 \\
		            				\hline
		            				Thickness (\si{\km}) & \textit{Annual} & 1.43 & \textit{Annual} & 1.03 & \textit{Semiannual},\newline \textit{Annual} & 0.612 & \textit{Annual} & 0.86 & \textit{Annual} & 1.03 \\
		            				\hline
		            				Top Temp (\si{\kelvin}) & \textit{Annual} & 75.87 & \textit{Annual} & 40.81 & \textit{Annual},\newline \textit{11-year} & 16.41 & \textit{Annual} & 33.72 & \textit{Annual} & 74.54 \\
		            				\hline
		            				Base Temp (\si{\kelvin}) & \textit{Annual} & 83.64 & \textit{Annual} & 50.45 & \textit{Semiannual},\newline \textit{Annual},\newline \textit{11-year} & 23.72 & \textit{Annual} & 38.94 & \textit{Annual} & 81.44 \\
		            				\hline
		            				Temp Diff (\si{\kelvin}) & \textit{Semiannual},\newline \textit{Annual} & 18.6 & \textit{Semiannual},\newline \textit{Annual} & 12.16 & \textit{Semiannual},\newline \textit{Annual} & 23.72 & \textit{Semiannual} & 12.51 & \textit{Semiannual},\newline \textit{Annual} & 14.94 \\
		            				\hline
		            			\end{tabular}
		            		}% End of resizebox	
		            		\caption{Periodicity (types of variations) and the total difference (denoted as \textbf{Total Diff}) in MIL TH and BH, TT and BT, thickness (denoted as \textbf{Thickness}), and TDiff (denoted as \textbf{Temp Diff}) across different latitude bands.}
		            		\label{Tab:tB2}
		            	\end{table}
%		            	\begin{table}[htbp]
%		            		\centering
%		            		\resizebox{\textwidth}{!}{ % Adjust to fit the table within the text width
%		            			\fontsize{4}{6}\selectfont % Extremely small font size (4pt with 6pt line spacing)
%		            			\begin{tabular}{|>{\bfseries}l|p{1cm}|p{1cm}|p{1cm}|p{1cm}|p{1cm}|}
%		            				\hline
%		            				\multirow{2}{*}{\diagbox[width=1.5cm, height=0.3cm]{}{}} & \textbf{\ang{60}-\ang{83}N} & \textbf{\ang{30}-\ang{60}N} & \textbf{\ang{30}N-\ang{30}S} & \textbf{\ang{30}-\ang{60}S} & \textbf{\ang{60}-\ang{83}S} \\
%		            				\cline{2-6}
%		            				& \textbf{Total diff} & \textbf{Total diff} & \textbf{Total diff} & \textbf{Total diff} & \textbf{Total diff} \\
%		            				\hline
%		            				Top Height (\si{\km}) & 12.74 & 11.42 & 3.7 & 9.91 & 13.25 \\
%		            				\hline
%		            				Base Height (\si{\km}) & 13.66 & 11.92 & 3.43 & 10.26 & 13.72 \\
%		            				\hline
%		            				Thickness (\si{\km}) & 1.43 & 1.03 & 0.612 & 0.86 & 1.03 \\
%		            				\hline
%		            				Top Temp (\si{\kelvin}) & 75.87 & 40.81 & 16.41 & 33.72 & 74.54 \\
%		            				\hline
%		            				Base Temp (\si{\kelvin}) & 83.64 & 50.45 & 23.72 & 38.94 & 81.44 \\
%		            				\hline
%		            				Temp Diff (\si{\kelvin}) & 18.6 & 12.16 & 23.72 & 12.51 & 14.94 \\
%		            				\hline
%		            			\end{tabular}
%		            		}% End of resizebox	
%		            		\caption{\textbf{Tab 1:} The total difference (denoted as \textbf{Total Diff}) in MIL TH and BH, TT and BT, thickness (denoted as \textbf{Thickness}), and TDiff (denoted as \textbf{Temp Diff}) across different latitude bands.}
%		            		\label{Tab:tB2}
%		            	\end{table}
		            \end{center}
				\end{block}
				
				\begin{block}{\small{Long-Term Trends \& External Influences}}
					\RaggedRight\small
						\begin{figure}
							\centering
							\includegraphics[width=0.95\textwidth, height=0.09\textheight, keepaspectratio=false]{All_Year_Trend_Spectral_Analysis_Ratio_Plot.jpeg} % Reduced size
							\caption{\textbf{Fig 10:} Time series and regression analysis of MIL occurrences from 2002 to 2023. (a) Monthly occurrences (blue) and linear trend (red). (b) Power spectrum showing 11-year solar cycle (black dot), annual (blue dot), and semiannual (green dot) variations.}
							\label{fig:wavelet}
						\end{figure}
						
						
						\small{
							\begin{itemize}[leftmargin=*, nosep]
								% All trends below are now stated as "per year" based on the image data
								\item \textbf{Global Trend:} Increase of $\sim 0.042 \pm 0.022$\% per year.
								\item \textbf{Dominant Periodicities:} 11-year (solar cycle), annual, semiannual.
								\item \textbf{Latitudinal Trends (per year, from table data):}.
								\scriptsize{
								\begin{itemize}
									\item \textbf{Tropics (30°N-S):} Increase ($0.046 \pm 0.043$\% ).
									\item \textbf{N-Mid (30-60°N):} Increase ($0.023 \pm 0.061$\% ).
									\item \textbf{S-Mid (30-60°S):} Decrease ($-0.008 \pm 0.048$\% ). % -7.96E-03 rounded
									\item \textbf{N-Polar (60-83°N):} Weak decrease ($-0.0006 \pm 0.029$\% ).
									\item \textbf{S-Polar (60-83°S):} Weakening trend ($-0.019 \pm 0.027$\% ). % -1.86E-02 rounded
								\end{itemize}
							}
							\end{itemize}
						}
						
%						\begin{center}
%							\tiny % Further reduce font size for the entire table environment
%							\setlength{\tabcolsep}{0.5pt} % Drastically Reduce space between columns
%							% Values updated from the provided image data, using scientific notation.
%							% Aiming for 1-2 significant figures in mantissa for small numbers to save space.
%							\begin{tabular}{|>{\bfseries\tiny}l|>{\tiny}c|>{\tiny}c|>{\tiny}c|>{\tiny}c|>{\tiny}c|>{\tiny}c|} % All content tiny
%								\hline
%								\diagbox[width=1.8cm, height=0.8cm]{\tiny Factor}{\tiny Region} % Made diagbox smaller
%								&\textbf{\tiny Glob} & \textbf{\tiny NPol} & \textbf{\tiny NMid} & \textbf{\tiny Trop} & \textbf{\tiny SMid} & \textbf{\tiny SPol} \\ % Abbreviated headers
%								\hline
%								Trend        & $\sci{4}{-4}$$\pm$$\sci{4}{-4}$  & $\sci{-6}{-4}$$\pm$$\sci{3}{-2}$ & $\sci{2}{-2}$$\pm$$\sci{6}{-2}$  & $\sci{5}{-2}$$\pm$$\sci{4}{-2}$  & $\sci{-8}{-3}$$\pm$$\sci{5}{-2}$ & $\sci{-2}{-2}$$\pm$$\sci{3}{-2}$ \\
%								\hline
%								Solar Flux   & $\sci{4}{-3}$$\pm$$\sci{9}{-4}$  & $\sci{6}{-2}$$\pm$$\sci{7}{-2}$  & $\sci{1}{-1}$$\pm$$\sci{1}{-1}$  & $\sci{8}{-2}$$\pm$$\sci{1}{-1}$  & $\sci{8}{-2}$$\pm$$\sci{1}{-1}$  & $\sci{1}{-2}$$\pm$$\sci{6}{-2}$  \\
%								\hline
%								QBO 30mb     & $\sci{6}{-4}$$\pm$$\sci{2}{-3}$  & $\sci{4}{-2}$$\pm$$\sci{2}{-1}$  & $\sci{-1}{-2}$$\pm$$\sci{3}{-1}$ & $\sci{8}{-2}$$\pm$$\sci{2}{-1}$  & $\sci{-3}{-2}$$\pm$$\sci{3}{-1}$ & $\sci{-1}{-2}$$\pm$$\sci{1}{-1}$ \\
%								\hline
%								QBO 50mb     & $\sci{9}{-4}$$\pm$$\sci{4}{-3}$  & $\sci{1}{-1}$$\pm$$\sci{3}{-1}$  & $\sci{8}{-3}$$\pm$$\sci{6}{-1}$  & $\sci{3}{-1}$$\pm$$\sci{4}{-1}$  & $\sci{-2}{-1}$$\pm$$\sci{5}{-1}$ & $\sci{-1}{-1}$$\pm$$\sci{3}{-1}$ \\
%								\hline
%								ENSO         & $\sci{1}{-2}$$\pm$$\sci{4}{-2}$  & $\sci{7}{-1}$$\pm$$\sci{3}{0}$   & $\sci{3}{0}$$\pm$$\sci{5}{0}$   & $\sci{-2}{0}$$\pm$$\sci{4}{0}$  & $\sci{-8}{-1}$$\pm$$\sci{4}{0}$  & $\sci{3}{-1}$$\pm$$\sci{2}{0}$  \\
%								\hline
%							\end{tabular}
%							\captionof{table}{\tiny Trends and sensitivity coefficients (annualized where applicable, per year) between various parameters (Solar Flux, QBO, ENSO) and MIL occurrences in different latitudinal bands. Values are Coefficient $\pm$ 95\% Confidence Interval Margin (1.96$\times$StdErr). Original StdErr, tStat, pValue for predictors are per original unit of predictor.}
%							\label{tab:mil_coefficients_sci_compact}
%						\end{center}
						
%					\scriptsize{
%					\begin{itemize}[leftmargin=*, nosep]
%						\item \textbf{Global Trend:} Increase of $\sim$534±110 MILs per decade (4.45±0.92 per month)
%						\item \textbf{Dominant Periodicities:} 11-year (solar cycle), annual, semiannual
%						\item \textbf{Latitudinal Trends:}
%						\begin{itemize}
%							\item \textbf{Tropics (30°N-S):} Steepest increase (2.86±0.80 per month)
%							\item \textbf{N-Mid (30-60°N):} Increase (1.41±1.11)
%							\item \textbf{S-Mid (30-60°S):} Moderate increase (0.38±0.87)
%							\item \textbf{N-Polar (60-83°N):} Weak increase (0.22±0.51)
%							\item \textbf{S-Polar (60-83°S):} Weakening trend (-0.41±0.48)
%						\end{itemize}
%					\end{itemize}
%							}
%					
%					\begin{center}
%						\scriptsize % Reduce font size for the table
%						\setlength{\tabcolsep}{1.5pt} % Reduce space between columns
%						% Note: Ensure regular spaces are used within the table data if manually typed
%						\begin{tabular}{|>{\bfseries\scriptsize}l|r|r|r|r|r|r|} % First col labels: bold, scriptsize
%							\hline
%							\diagbox[width=2cm, height=1cm]{\tiny}{\tiny} % Smaller diagbox, tiny text inside
%							&\textbf{\tiny Global} & \textbf{\tiny 60-83°N} & \textbf{\tiny 30-60°N} & \textbf{\tiny 30°N-30°S} & \textbf{\tiny 30-60°S} & \textbf{\tiny 60-83°S} \\
%							\hline
%							Trend        & 0.0$\pm$0.92     & 0.22$\pm$0.51     & 1.41$\pm$1.11     & 2.86$\pm$0.80         & 0.38$\pm$0.87         & -0.41$\pm$0.48    \\
%							\hline
%							Solar Flux   & 9.33$\pm$2.08     & 1.74$\pm$1.14     & 4.32$\pm$2.48     & 1.59$\pm$1.83         & 2.10$\pm$1.95         & 0.04$\pm$1.07     \\
%							\hline
%							QBO 30 mb    & 0.97$\pm$5.17     & 1.43$\pm$2.75     & -0.32$\pm$5.98    & 2.41$\pm$4.38         & -1.42$\pm$4.69        & -0.78$\pm$2.57    \\
%							\hline
%							QBO 50 mb    & 2.89$\pm$9.29     &4.51$\pm$4.94      & 1.40$\pm$10.74    & 9.30$\pm$7.86         & -8.72$\pm$8.42        & -4.43$\pm$4.60    \\
%							\hline
%							ENSO         & -2.32$\pm$83.17   &21.33$\pm$44.24    & 97.08$\pm$95.97   & -90.65$\pm$70.31  & -42.59$\pm$75.46  & 3.63$\pm$41.28    \\
%							\hline
%						\end{tabular}
%						\captionof{table}{Trends coefficients per year between various parameters (Solar Flux, QBO, ENSO) and MIL occurrences in different latitudinal bands.}
%					\end{center}
				\end{block}
				
				
				\begin{highlightblock}{\small{Key Findings \& Conclusions}}
					\RaggedRight\small % Using \small for consistency
					\begin{itemize}[leftmargin=*, nosep]
						\item The 22-year SABER data reveals distinct hemispherical asymmetry and seasonal and latitudinal patterns in MILs.
						\item Tropics: Highest MILs at equinoxes, largest $\Delta T$ ($\sim$24K), smallest thickness ($\sim$0.61km).
						\item Mid-latitudes and polar regions: MILs peak in autumn and winter, with a minimum in spring and summer.
						\item Global MILs increased by $\sim 0.42 \pm 0.44$ percent per decade, strongly modulated by the 11-year solar cycle.
						\item ENSO negatively modulates MILs; QBO shows positive (and some negative) modulation depending on latitude.
						\item Findings highlight complex interplay of solar activity, large-scale oscillations, and wave dynamics in MIL formation.
						\item Long-term monitoring of MILs provides valuable insights into middle atmosphere dynamics and climate change impacts.
					\end{itemize}
				\end{highlightblock}
				
			\end{column}
			
			\begin{column}{0.01\textwidth}
			\end{column}
		\end{columns}
		% ADD THE ACKNOWLEDGMENTS SECTION HERE
		\vfill % This will push the acknowledgments to the bottom of the available frame body space
		\vspace{-4.1cm} % Specify the desired fixed space, e.g., 2cm. Adjust as needed.
		
		\noindent % Prevent paragraph indentation
		\hspace*{0.01\linewidth}% This compensates for the 0.01\textwidth left spacer column, aligning with the first *content* column.
		\begin{minipage}{\dimexpr0.39\linewidth + \columnsep + 0.32\linewidth\relax}
		% The width of the minipage is set to be the sum of the widths of the
		% first content column (0.32\linewidth), the column separator (\columnsep),
		% and the second content column (0.32\linewidth).
		%\begin{center}
		\RaggedRight
		\tiny{ % The font size setting as in the original
			\begin{tikzpicture}
				% \linewidth for 'text width' here refers to the width of the enclosing minipage.
				% The 0.9 factor means the colored box will be 90% of the minipage's width.
				\node[draw=primarycolor, thick, fill=headerbg, rounded corners, inner sep=10pt, align=center, text width=0.9\linewidth] {
					\textbf{\small{Acknowledgments}}\\[0.2cm]
					\small{This research was supported by the Divisão de Clima Espacial, Instituto Nacional de Pesquisas Espaciais (INPE), CAPES, CNPq, MCTI, and AEB.	We thank the TIMED/SABER team for providing the data used in this study.}
				};
			\end{tikzpicture}
		} % End of \tiny scope
		%\end{center}
		\end{minipage}
		% The rest of the line (to the right of this minipage) will be empty.
		
		\end{frame} % <<<< THIS IS THE END OF THE FRAME
	
\end{document}